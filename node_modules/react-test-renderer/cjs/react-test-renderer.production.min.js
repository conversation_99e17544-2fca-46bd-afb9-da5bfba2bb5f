/**
 * @license React
 * react-test-renderer.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("react"),ba=require("scheduler/unstable_mock"),ca=require("scheduler");function da(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1;d.configurable=!0;"value"in d&&(d.writable=!0);Object.defineProperty(a,d.key,d)}}function ea(a,b,c){b&&da(a.prototype,b);c&&da(a,c);return a}
var fa=Object.assign,ha=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ia=Symbol.for("react.element"),ja=Symbol.for("react.portal"),ka=Symbol.for("react.fragment"),la=Symbol.for("react.strict_mode"),ma=Symbol.for("react.profiler"),na=Symbol.for("react.provider"),oa=Symbol.for("react.context"),pa=Symbol.for("react.forward_ref"),qa=Symbol.for("react.suspense"),ra=Symbol.for("react.suspense_list"),sa=Symbol.for("react.memo"),ta=Symbol.for("react.lazy");Symbol.for("react.scope");Symbol.for("react.debug_trace_mode");
var ua=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden");Symbol.for("react.cache");Symbol.for("react.tracing_marker");var va=Symbol.iterator;function wa(a){if(null===a||"object"!==typeof a)return null;a=va&&a[va]||a["@@iterator"];return"function"===typeof a?a:null}
function xa(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case ka:return"Fragment";case ja:return"Portal";case ma:return"Profiler";case la:return"StrictMode";case qa:return"Suspense";case ra:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case oa:return(a.displayName||"Context")+".Consumer";case na:return(a._context.displayName||"Context")+".Provider";case pa:var b=a.render;a=a.displayName;a||(a=b.displayName||
b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case sa:return b=a.displayName||null,null!==b?b:xa(a.type)||"Memo";case ta:b=a._payload;a=a._init;try{return xa(a(b))}catch(c){}}return null}
function ya(a){var b=a.type;switch(a.tag){case 24:return"Cache";case 9:return(b.displayName||"Context")+".Consumer";case 10:return(b._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return a=b.render,a=a.displayName||a.name||"",b.displayName||(""!==a?"ForwardRef("+a+")":"ForwardRef");case 7:return"Fragment";case 5:return b;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xa(b);case 8:return b===la?"StrictMode":"Mode";case 22:return"Offscreen";
case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof b)return b.displayName||b.name||null;if("string"===typeof b)return b}return null}function za(a){var b=a,c=a;if(a.alternate)for(;b.return;)b=b.return;else{a=b;do b=a,0!==(b.flags&4098)&&(c=b.return),a=b.return;while(a)}return 3===b.tag?c:null}
function Aa(a){if(za(a)!==a)throw Error("Unable to find node on an unmounted component.");}
function Ba(a){var b=a.alternate;if(!b){b=za(a);if(null===b)throw Error("Unable to find node on an unmounted component.");return b!==a?null:a}for(var c=a,d=b;;){var e=c.return;if(null===e)break;var f=e.alternate;if(null===f){d=e.return;if(null!==d){c=d;continue}break}if(e.child===f.child){for(f=e.child;f;){if(f===c)return Aa(e),a;if(f===d)return Aa(e),b;f=f.sibling}throw Error("Unable to find node on an unmounted component.");}if(c.return!==d.return)c=e,d=f;else{for(var g=!1,h=e.child;h;){if(h===
c){g=!0;c=e;d=f;break}if(h===d){g=!0;d=e;c=f;break}h=h.sibling}if(!g){for(h=f.child;h;){if(h===c){g=!0;c=f;d=e;break}if(h===d){g=!0;d=f;c=e;break}h=h.sibling}if(!g)throw Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.");}}if(c.alternate!==d)throw Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.");}if(3!==c.tag)throw Error("Unable to find node on an unmounted component.");
return c.stateNode.current===c?a:b}function Ca(a){if(5===a.tag||6===a.tag)return a;for(a=a.child;null!==a;){var b=Ca(a);if(null!==b)return b;a=a.sibling}return null}var Da=Array.isArray,Ea=ca.unstable_scheduleCallback,Fa=ca.unstable_cancelCallback,Ga=ca.unstable_shouldYield,Ha=ca.unstable_requestPaint,q=ca.unstable_now,Ia=ca.unstable_ImmediatePriority,Ja=ca.unstable_UserBlockingPriority,La=ca.unstable_NormalPriority,Ma=ca.unstable_IdlePriority,Na=null,Oa=null;
function Pa(a){if(Oa&&"function"===typeof Oa.onCommitFiberRoot)try{Oa.onCommitFiberRoot(Na,a,void 0,128===(a.current.flags&128))}catch(b){}}var Ra=Math.clz32?Math.clz32:Qa,Sa=Math.log,Ta=Math.LN2;function Qa(a){a>>>=0;return 0===a?32:31-(Sa(a)/Ta|0)|0}var Ua=64,Va=4194304;
function Wa(a){switch(a&-a){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return a&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return a&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;
default:return a}}function Xa(a,b){var c=a.pendingLanes;if(0===c)return 0;var d=0,e=a.suspendedLanes,f=a.pingedLanes,g=c&268435455;if(0!==g){var h=g&~e;0!==h?d=Wa(h):(f&=g,0!==f&&(d=Wa(f)))}else g=c&~e,0!==g?d=Wa(g):0!==f&&(d=Wa(f));if(0===d)return 0;if(0!==b&&b!==d&&0===(b&e)&&(e=d&-d,f=b&-b,e>=f||16===e&&0!==(f&4194240)))return b;0!==(d&4)&&(d|=c&16);b=a.entangledLanes;if(0!==b)for(a=a.entanglements,b&=d;0<b;)c=31-Ra(b),e=1<<c,d|=a[c],b&=~e;return d}
function Za(a,b){switch(a){case 1:case 2:case 4:return b+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return b+5E3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}
function $a(a){a=a.pendingLanes&-1073741825;return 0!==a?a:a&1073741824?1073741824:0}function ab(){var a=Ua;Ua<<=1;0===(Ua&4194240)&&(Ua=64);return a}function bb(a){for(var b=[],c=0;31>c;c++)b.push(a);return b}function cb(a,b,c){a.pendingLanes|=b;536870912!==b&&(a.suspendedLanes=0,a.pingedLanes=0);a=a.eventTimes;b=31-Ra(b);a[b]=c}
function db(a,b){var c=a.pendingLanes&~b;a.pendingLanes=b;a.suspendedLanes=0;a.pingedLanes=0;a.expiredLanes&=b;a.mutableReadLanes&=b;a.entangledLanes&=b;b=a.entanglements;var d=a.eventTimes;for(a=a.expirationTimes;0<c;){var e=31-Ra(c),f=1<<e;b[e]=0;d[e]=-1;a[e]=-1;c&=~f}}function eb(a,b){var c=a.entangledLanes|=b;for(a=a.entanglements;c;){var d=31-Ra(c),e=1<<d;e&b|a[d]&b&&(a[d]|=b);c&=~e}}var v=0;function fb(a){a&=-a;return 1<a?4<a?0!==(a&268435455)?16:536870912:4:1}
function gb(){throw Error("The current renderer does not support hydration. This error is likely caused by a bug in React. Please file an issue.");}var ib={},jb={},kb=new WeakMap;function lb(a){switch(a.tag){case "INSTANCE":var b=a.rootContainerInstance.createNodeMock;b=b({type:a.type,props:a.props});"object"===typeof b&&null!==b&&kb.set(b,a);return b;default:return a}}function mb(a,b){var c=a.children.indexOf(b);-1!==c&&a.children.splice(c,1);a.children.push(b)}
function nb(a,b,c){var d=a.children.indexOf(b);-1!==d&&a.children.splice(d,1);c=a.children.indexOf(c);a.children.splice(c,0,b)}var ob=setTimeout,pb=clearTimeout,qb;function rb(a){if(void 0===qb)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);qb=b&&b[1]||""}return"\n"+qb+a}var sb=!1;
function tb(a,b){if(!a||sb)return"";sb=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(b)if(b=function(){throw Error();},Object.defineProperty(b.prototype,"props",{set:function(){throw Error();}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(b,[])}catch(l){var d=l}Reflect.construct(a,[],b)}else{try{b.call()}catch(l){d=l}a.call(b.prototype)}else{try{throw Error();}catch(l){d=l}a()}}catch(l){if(l&&d&&"string"===typeof l.stack){for(var e=l.stack.split("\n"),
f=d.stack.split("\n"),g=e.length-1,h=f.length-1;1<=g&&0<=h&&e[g]!==f[h];)h--;for(;1<=g&&0<=h;g--,h--)if(e[g]!==f[h]){if(1!==g||1!==h){do if(g--,h--,0>h||e[g]!==f[h]){var k="\n"+e[g].replace(" at new "," at ");a.displayName&&k.includes("<anonymous>")&&(k=k.replace("<anonymous>",a.displayName));return k}while(1<=g&&0<=h)}break}}}finally{sb=!1,Error.prepareStackTrace=c}return(a=a?a.displayName||a.name:"")?rb(a):""}var ub=Object.prototype.hasOwnProperty,vb=[],wb=-1;function xb(a){return{current:a}}
function x(a){0>wb||(a.current=vb[wb],vb[wb]=null,wb--)}function y(a,b){wb++;vb[wb]=a.current;a.current=b}var yb={},z=xb(yb),C=xb(!1),zb=yb;function Ab(a,b){var c=a.type.contextTypes;if(!c)return yb;var d=a.stateNode;if(d&&d.__reactInternalMemoizedUnmaskedChildContext===b)return d.__reactInternalMemoizedMaskedChildContext;var e={},f;for(f in c)e[f]=b[f];d&&(a=a.stateNode,a.__reactInternalMemoizedUnmaskedChildContext=b,a.__reactInternalMemoizedMaskedChildContext=e);return e}
function D(a){a=a.childContextTypes;return null!==a&&void 0!==a}function Bb(){x(C);x(z)}function Cb(a,b,c){if(z.current!==yb)throw Error("Unexpected context found on stack. This error is likely caused by a bug in React. Please file an issue.");y(z,b);y(C,c)}
function Db(a,b,c){var d=a.stateNode;b=b.childContextTypes;if("function"!==typeof d.getChildContext)return c;d=d.getChildContext();for(var e in d)if(!(e in b))throw Error((ya(a)||"Unknown")+'.getChildContext(): key "'+e+'" is not defined in childContextTypes.');return fa({},c,d)}function Eb(a){a=(a=a.stateNode)&&a.__reactInternalMemoizedMergedChildContext||yb;zb=z.current;y(z,a);y(C,C.current);return!0}
function Fb(a,b,c){var d=a.stateNode;if(!d)throw Error("Expected to have an instance by this point. This error is likely caused by a bug in React. Please file an issue.");c?(a=Db(a,b,zb),d.__reactInternalMemoizedMergedChildContext=a,x(C),x(z),y(z,a)):x(C);y(C,c)}function Gb(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Hb="function"===typeof Object.is?Object.is:Gb,E=null,Ib=!1,Jb=!1;
function Kb(){if(!Jb&&null!==E){Jb=!0;var a=0,b=v;try{var c=E;for(v=1;a<c.length;a++){var d=c[a];do d=d(!0);while(null!==d)}E=null;Ib=!1}catch(e){throw null!==E&&(E=E.slice(a+1)),Ea(Ia,Kb),e;}finally{v=b,Jb=!1}}return null}var Lb=[],Mb=0,Nb=null,Ob=[],Pb=0,Qb=null;function Rb(a){for(;a===Nb;)Nb=Lb[--Mb],Lb[Mb]=null,--Mb,Lb[Mb]=null;for(;a===Qb;)Qb=Ob[--Pb],Ob[Pb]=null,--Pb,Ob[Pb]=null,--Pb,Ob[Pb]=null}var Sb=null,Tb=ha.ReactCurrentBatchConfig;
function Ub(a,b){if(Hb(a,b))return!0;if("object"!==typeof a||null===a||"object"!==typeof b||null===b)return!1;var c=Object.keys(a),d=Object.keys(b);if(c.length!==d.length)return!1;for(d=0;d<c.length;d++){var e=c[d];if(!ub.call(b,e)||!Hb(a[e],b[e]))return!1}return!0}
function Vb(a){switch(a.tag){case 5:return rb(a.type);case 16:return rb("Lazy");case 13:return rb("Suspense");case 19:return rb("SuspenseList");case 0:case 2:case 15:return a=tb(a.type,!1),a;case 11:return a=tb(a.type.render,!1),a;case 1:return a=tb(a.type,!0),a;default:return""}}function Wb(a,b){if(a&&a.defaultProps){b=fa({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}var Xb=xb(null),Yb=null,Zb=null,$b=null;function ac(){$b=Zb=Yb=null}
function bc(a){var b=Xb.current;x(Xb);a._currentValue2=b}function cc(a,b,c){for(;null!==a;){var d=a.alternate;(a.childLanes&b)!==b?(a.childLanes|=b,null!==d&&(d.childLanes|=b)):null!==d&&(d.childLanes&b)!==b&&(d.childLanes|=b);if(a===c)break;a=a.return}}function dc(a,b){Yb=a;$b=Zb=null;a=a.dependencies;null!==a&&null!==a.firstContext&&(0!==(a.lanes&b)&&(F=!0),a.firstContext=null)}
function H(a){var b=a._currentValue2;if($b!==a)if(a={context:a,memoizedValue:b,next:null},null===Zb){if(null===Yb)throw Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");Zb=a;Yb.dependencies={lanes:0,firstContext:a}}else Zb=Zb.next=a;return b}var ec=null;
function fc(a){null===ec?ec=[a]:ec.push(a)}function gc(a,b,c,d){var e=b.interleaved;null===e?(c.next=c,fc(b)):(c.next=e.next,e.next=c);b.interleaved=c;return hc(a,d)}function hc(a,b){a.lanes|=b;var c=a.alternate;null!==c&&(c.lanes|=b);c=a;for(a=a.return;null!==a;)a.childLanes|=b,c=a.alternate,null!==c&&(c.childLanes|=b),c=a,a=a.return;return 3===c.tag?c.stateNode:null}var ic=!1;
function jc(a){a.updateQueue={baseState:a.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function kc(a,b){a=a.updateQueue;b.updateQueue===a&&(b.updateQueue={baseState:a.baseState,firstBaseUpdate:a.firstBaseUpdate,lastBaseUpdate:a.lastBaseUpdate,shared:a.shared,effects:a.effects})}function lc(a,b){return{eventTime:a,lane:b,tag:0,payload:null,callback:null,next:null}}
function mc(a,b,c){var d=a.updateQueue;if(null===d)return null;d=d.shared;if(0!==(I&2)){var e=d.pending;null===e?b.next=b:(b.next=e.next,e.next=b);d.pending=b;return hc(a,c)}e=d.interleaved;null===e?(b.next=b,fc(d)):(b.next=e.next,e.next=b);d.interleaved=b;return hc(a,c)}function nc(a,b,c){b=b.updateQueue;if(null!==b&&(b=b.shared,0!==(c&4194240))){var d=b.lanes;d&=a.pendingLanes;c|=d;b.lanes=c;eb(a,c)}}
function oc(a,b){var c=a.updateQueue,d=a.alternate;if(null!==d&&(d=d.updateQueue,c===d)){var e=null,f=null;c=c.firstBaseUpdate;if(null!==c){do{var g={eventTime:c.eventTime,lane:c.lane,tag:c.tag,payload:c.payload,callback:c.callback,next:null};null===f?e=f=g:f=f.next=g;c=c.next}while(null!==c);null===f?e=f=b:f=f.next=b}else e=f=b;c={baseState:d.baseState,firstBaseUpdate:e,lastBaseUpdate:f,shared:d.shared,effects:d.effects};a.updateQueue=c;return}a=c.lastBaseUpdate;null===a?c.firstBaseUpdate=b:a.next=
b;c.lastBaseUpdate=b}
function pc(a,b,c,d){var e=a.updateQueue;ic=!1;var f=e.firstBaseUpdate,g=e.lastBaseUpdate,h=e.shared.pending;if(null!==h){e.shared.pending=null;var k=h,l=k.next;k.next=null;null===g?f=l:g.next=l;g=k;var m=a.alternate;null!==m&&(m=m.updateQueue,h=m.lastBaseUpdate,h!==g&&(null===h?m.firstBaseUpdate=l:h.next=l,m.lastBaseUpdate=k))}if(null!==f){var w=e.baseState;g=0;m=l=k=null;h=f;do{var p=h.lane,B=h.eventTime;if((d&p)===p){null!==m&&(m=m.next={eventTime:B,lane:0,tag:h.tag,payload:h.payload,callback:h.callback,
next:null});a:{var r=a,G=h;p=b;B=c;switch(G.tag){case 1:r=G.payload;if("function"===typeof r){w=r.call(B,w,p);break a}w=r;break a;case 3:r.flags=r.flags&-65537|128;case 0:r=G.payload;p="function"===typeof r?r.call(B,w,p):r;if(null===p||void 0===p)break a;w=fa({},w,p);break a;case 2:ic=!0}}null!==h.callback&&0!==h.lane&&(a.flags|=64,p=e.effects,null===p?e.effects=[h]:p.push(h))}else B={eventTime:B,lane:p,tag:h.tag,payload:h.payload,callback:h.callback,next:null},null===m?(l=m=B,k=w):m=m.next=B,g|=
p;h=h.next;if(null===h)if(h=e.shared.pending,null===h)break;else p=h,h=p.next,p.next=null,e.lastBaseUpdate=p,e.shared.pending=null}while(1);null===m&&(k=w);e.baseState=k;e.firstBaseUpdate=l;e.lastBaseUpdate=m;b=e.shared.interleaved;if(null!==b){e=b;do g|=e.lane,e=e.next;while(e!==b)}else null===f&&(e.shared.lanes=0);J|=g;a.lanes=g;a.memoizedState=w}}
function qc(a,b,c){a=b.effects;b.effects=null;if(null!==a)for(b=0;b<a.length;b++){var d=a[b],e=d.callback;if(null!==e){d.callback=null;if("function"!==typeof e)throw Error("Invalid argument passed as callback. Expected a function. Instead received: "+e);e.call(c)}}}var rc=(new aa.Component).refs;function sc(a,b,c,d){b=a.memoizedState;c=c(d,b);c=null===c||void 0===c?b:fa({},b,c);a.memoizedState=c;0===a.lanes&&(a.updateQueue.baseState=c)}
var wc={isMounted:function(a){return(a=a._reactInternals)?za(a)===a:!1},enqueueSetState:function(a,b,c){a=a._reactInternals;var d=tc(),e=uc(a),f=lc(d,e);f.payload=b;void 0!==c&&null!==c&&(f.callback=c);b=mc(a,f,e);null!==b&&(vc(b,a,e,d),nc(b,a,e))},enqueueReplaceState:function(a,b,c){a=a._reactInternals;var d=tc(),e=uc(a),f=lc(d,e);f.tag=1;f.payload=b;void 0!==c&&null!==c&&(f.callback=c);b=mc(a,f,e);null!==b&&(vc(b,a,e,d),nc(b,a,e))},enqueueForceUpdate:function(a,b){a=a._reactInternals;var c=tc(),
d=uc(a),e=lc(c,d);e.tag=2;void 0!==b&&null!==b&&(e.callback=b);b=mc(a,e,d);null!==b&&(vc(b,a,d,c),nc(b,a,d))}};function xc(a,b,c,d,e,f,g){a=a.stateNode;return"function"===typeof a.shouldComponentUpdate?a.shouldComponentUpdate(d,f,g):b.prototype&&b.prototype.isPureReactComponent?!Ub(c,d)||!Ub(e,f):!0}
function yc(a,b,c){var d=!1,e=yb;var f=b.contextType;"object"===typeof f&&null!==f?f=H(f):(e=D(b)?zb:z.current,d=b.contextTypes,f=(d=null!==d&&void 0!==d)?Ab(a,e):yb);b=new b(c,f);a.memoizedState=null!==b.state&&void 0!==b.state?b.state:null;b.updater=wc;a.stateNode=b;b._reactInternals=a;d&&(a=a.stateNode,a.__reactInternalMemoizedUnmaskedChildContext=e,a.__reactInternalMemoizedMaskedChildContext=f);return b}
function zc(a,b,c,d){a=b.state;"function"===typeof b.componentWillReceiveProps&&b.componentWillReceiveProps(c,d);"function"===typeof b.UNSAFE_componentWillReceiveProps&&b.UNSAFE_componentWillReceiveProps(c,d);b.state!==a&&wc.enqueueReplaceState(b,b.state,null)}
function Ac(a,b,c,d){var e=a.stateNode;e.props=c;e.state=a.memoizedState;e.refs=rc;jc(a);var f=b.contextType;"object"===typeof f&&null!==f?e.context=H(f):(f=D(b)?zb:z.current,e.context=Ab(a,f));e.state=a.memoizedState;f=b.getDerivedStateFromProps;"function"===typeof f&&(sc(a,b,f,c),e.state=a.memoizedState);"function"===typeof b.getDerivedStateFromProps||"function"===typeof e.getSnapshotBeforeUpdate||"function"!==typeof e.UNSAFE_componentWillMount&&"function"!==typeof e.componentWillMount||(b=e.state,
"function"===typeof e.componentWillMount&&e.componentWillMount(),"function"===typeof e.UNSAFE_componentWillMount&&e.UNSAFE_componentWillMount(),b!==e.state&&wc.enqueueReplaceState(e,e.state,null),pc(a,c,e,d),e.state=a.memoizedState);"function"===typeof e.componentDidMount&&(a.flags|=4)}
function Bc(a,b,c){a=c.ref;if(null!==a&&"function"!==typeof a&&"object"!==typeof a){if(c._owner){c=c._owner;if(c){if(1!==c.tag)throw Error("Function components cannot have string refs. We recommend using useRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref");var d=c.stateNode}if(!d)throw Error("Missing owner for string ref "+a+". This error is likely caused by a bug in React. Please file an issue.");var e=d,f=""+a;if(null!==b&&null!==b.ref&&"function"===
typeof b.ref&&b.ref._stringRef===f)return b.ref;b=function(a){var b=e.refs;b===rc&&(b=e.refs={});null===a?delete b[f]:b[f]=a};b._stringRef=f;return b}if("string"!==typeof a)throw Error("Expected ref to be a function, a string, an object returned by React.createRef(), or null.");if(!c._owner)throw Error("Element ref was specified as a string ("+a+") but no owner was set. This could happen for one of the following reasons:\n1. You may be adding a ref to a function component\n2. You may be adding a ref to a component that was not created inside a component's render method\n3. You have multiple copies of React loaded\nSee https://reactjs.org/link/refs-must-have-owner for more information.");
}return a}function Cc(a,b){a=Object.prototype.toString.call(b);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===a?"object with keys {"+Object.keys(b).join(", ")+"}":a)+"). If you meant to render a collection of children, use an array instead.");}function Dc(a){var b=a._init;return b(a._payload)}
function Ec(a){function b(b,c){if(a){var d=b.deletions;null===d?(b.deletions=[c],b.flags|=16):d.push(c)}}function c(c,d){if(!a)return null;for(;null!==d;)b(c,d),d=d.sibling;return null}function d(a,b){for(a=new Map;null!==b;)null!==b.key?a.set(b.key,b):a.set(b.index,b),b=b.sibling;return a}function e(a,b){a=Fc(a,b);a.index=0;a.sibling=null;return a}function f(b,c,d){b.index=d;if(!a)return b.flags|=1048576,c;d=b.alternate;if(null!==d)return d=d.index,d<c?(b.flags|=2,c):d;b.flags|=2;return c}function g(b){a&&
null===b.alternate&&(b.flags|=2);return b}function h(a,b,c,d){if(null===b||6!==b.tag)return b=Gc(c,a.mode,d),b.return=a,b;b=e(b,c);b.return=a;return b}function k(a,b,c,d){var f=c.type;if(f===ka)return m(a,b,c.props.children,d,c.key);if(null!==b&&(b.elementType===f||"object"===typeof f&&null!==f&&f.$$typeof===ta&&Dc(f)===b.type))return d=e(b,c.props),d.ref=Bc(a,b,c),d.return=a,d;d=Hc(c.type,c.key,c.props,null,a.mode,d);d.ref=Bc(a,b,c);d.return=a;return d}function l(a,b,c,d){if(null===b||4!==b.tag||
b.stateNode.containerInfo!==c.containerInfo||b.stateNode.implementation!==c.implementation)return b=Ic(c,a.mode,d),b.return=a,b;b=e(b,c.children||[]);b.return=a;return b}function m(a,b,c,d,f){if(null===b||7!==b.tag)return b=Jc(c,a.mode,d,f),b.return=a,b;b=e(b,c);b.return=a;return b}function w(a,b,c){if("string"===typeof b&&""!==b||"number"===typeof b)return b=Gc(""+b,a.mode,c),b.return=a,b;if("object"===typeof b&&null!==b){switch(b.$$typeof){case ia:return c=Hc(b.type,b.key,b.props,null,a.mode,c),
c.ref=Bc(a,null,b),c.return=a,c;case ja:return b=Ic(b,a.mode,c),b.return=a,b;case ta:var d=b._init;return w(a,d(b._payload),c)}if(Da(b)||wa(b))return b=Jc(b,a.mode,c,null),b.return=a,b;Cc(a,b)}return null}function p(a,b,c,d){var e=null!==b?b.key:null;if("string"===typeof c&&""!==c||"number"===typeof c)return null!==e?null:h(a,b,""+c,d);if("object"===typeof c&&null!==c){switch(c.$$typeof){case ia:return c.key===e?k(a,b,c,d):null;case ja:return c.key===e?l(a,b,c,d):null;case ta:return e=c._init,p(a,
b,e(c._payload),d)}if(Da(c)||wa(c))return null!==e?null:m(a,b,c,d,null);Cc(a,c)}return null}function B(a,b,c,d,e){if("string"===typeof d&&""!==d||"number"===typeof d)return a=a.get(c)||null,h(b,a,""+d,e);if("object"===typeof d&&null!==d){switch(d.$$typeof){case ia:return a=a.get(null===d.key?c:d.key)||null,k(b,a,d,e);case ja:return a=a.get(null===d.key?c:d.key)||null,l(b,a,d,e);case ta:var f=d._init;return B(a,b,c,f(d._payload),e)}if(Da(d)||wa(d))return a=a.get(c)||null,m(b,a,d,e,null);Cc(b,d)}return null}
function r(e,g,h,k){for(var l=null,m=null,n=g,t=g=0,A=null;null!==n&&t<h.length;t++){n.index>t?(A=n,n=null):A=n.sibling;var u=p(e,n,h[t],k);if(null===u){null===n&&(n=A);break}a&&n&&null===u.alternate&&b(e,n);g=f(u,g,t);null===m?l=u:m.sibling=u;m=u;n=A}if(t===h.length)return c(e,n),l;if(null===n){for(;t<h.length;t++)n=w(e,h[t],k),null!==n&&(g=f(n,g,t),null===m?l=n:m.sibling=n,m=n);return l}for(n=d(e,n);t<h.length;t++)A=B(n,e,t,h[t],k),null!==A&&(a&&null!==A.alternate&&n.delete(null===A.key?t:A.key),
g=f(A,g,t),null===m?l=A:m.sibling=A,m=A);a&&n.forEach(function(a){return b(e,a)});return l}function G(e,g,h,k){var l=wa(h);if("function"!==typeof l)throw Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");h=l.call(h);if(null==h)throw Error("An iterable object provided no iterator.");for(var m=l=null,n=g,t=g=0,A=null,u=h.next();null!==n&&!u.done;t++,u=h.next()){n.index>t?(A=n,n=null):A=n.sibling;var r=p(e,n,u.value,k);if(null===r){null===n&&
(n=A);break}a&&n&&null===r.alternate&&b(e,n);g=f(r,g,t);null===m?l=r:m.sibling=r;m=r;n=A}if(u.done)return c(e,n),l;if(null===n){for(;!u.done;t++,u=h.next())u=w(e,u.value,k),null!==u&&(g=f(u,g,t),null===m?l=u:m.sibling=u,m=u);return l}for(n=d(e,n);!u.done;t++,u=h.next())u=B(n,e,t,u.value,k),null!==u&&(a&&null!==u.alternate&&n.delete(null===u.key?t:u.key),g=f(u,g,t),null===m?l=u:m.sibling=u,m=u);a&&n.forEach(function(a){return b(e,a)});return l}function Ka(a,d,f,h){"object"===typeof f&&null!==f&&f.type===
ka&&null===f.key&&(f=f.props.children);if("object"===typeof f&&null!==f){switch(f.$$typeof){case ia:a:{for(var k=f.key,l=d;null!==l;){if(l.key===k){k=f.type;if(k===ka){if(7===l.tag){c(a,l.sibling);d=e(l,f.props.children);d.return=a;a=d;break a}}else if(l.elementType===k||"object"===typeof k&&null!==k&&k.$$typeof===ta&&Dc(k)===l.type){c(a,l.sibling);d=e(l,f.props);d.ref=Bc(a,l,f);d.return=a;a=d;break a}c(a,l);break}else b(a,l);l=l.sibling}f.type===ka?(d=Jc(f.props.children,a.mode,h,f.key),d.return=
a,a=d):(h=Hc(f.type,f.key,f.props,null,a.mode,h),h.ref=Bc(a,d,f),h.return=a,a=h)}return g(a);case ja:a:{for(l=f.key;null!==d;){if(d.key===l)if(4===d.tag&&d.stateNode.containerInfo===f.containerInfo&&d.stateNode.implementation===f.implementation){c(a,d.sibling);d=e(d,f.children||[]);d.return=a;a=d;break a}else{c(a,d);break}else b(a,d);d=d.sibling}d=Ic(f,a.mode,h);d.return=a;a=d}return g(a);case ta:return l=f._init,Ka(a,d,l(f._payload),h)}if(Da(f))return r(a,d,f,h);if(wa(f))return G(a,d,f,h);Cc(a,f)}return"string"===
typeof f&&""!==f||"number"===typeof f?(f=""+f,null!==d&&6===d.tag?(c(a,d.sibling),d=e(d,f),d.return=a,a=d):(c(a,d),d=Gc(f,a.mode,h),d.return=a,a=d),g(a)):c(a,d)}return Ka}var Kc=Ec(!0),Lc=Ec(!1),Mc={},Nc=xb(Mc),Oc=xb(Mc),Pc=xb(Mc);function Qc(a){if(a===Mc)throw Error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue.");return a}function Rc(a,b){y(Pc,b);y(Oc,a);y(Nc,Mc);x(Nc);y(Nc,ib)}function Sc(){x(Nc);x(Oc);x(Pc)}
function Tc(a){Qc(Pc.current);Qc(Nc.current)!==ib&&(y(Oc,a),y(Nc,ib))}function Uc(a){Oc.current===a&&(x(Nc),x(Oc))}var K=xb(0);
function Vc(a){for(var b=a;null!==b;){if(13===b.tag){var c=b.memoizedState;if(null!==c&&(null===c.dehydrated||gb()||gb()))return b}else if(19===b.tag&&void 0!==b.memoizedProps.revealOrder){if(0!==(b.flags&128))return b}else if(null!==b.child){b.child.return=b;b=b.child;continue}if(b===a)break;for(;null===b.sibling;){if(null===b.return||b.return===a)return null;b=b.return}b.sibling.return=b.return;b=b.sibling}return null}var Wc=[];
function Xc(){for(var a=0;a<Wc.length;a++)Wc[a]._workInProgressVersionSecondary=null;Wc.length=0}var Yc=ha.ReactCurrentDispatcher,Zc=ha.ReactCurrentBatchConfig,$c=0,L=null,M=null,N=null,ad=!1,bd=!1,cd=0;
function O(){throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");}
function dd(a,b){if(null===b)return!1;for(var c=0;c<b.length&&c<a.length;c++)if(!Hb(a[c],b[c]))return!1;return!0}
function ed(a,b,c,d,e,f){$c=f;L=b;b.memoizedState=null;b.updateQueue=null;b.lanes=0;Yc.current=null===a||null===a.memoizedState?fd:gd;a=c(d,e);if(bd){f=0;do{bd=!1;if(25<=f)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");f+=1;N=M=null;b.updateQueue=null;Yc.current=hd;a=c(d,e)}while(bd)}Yc.current=id;b=null!==M&&null!==M.next;$c=0;N=M=L=null;ad=!1;if(b)throw Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");
return a}function jd(){var a={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};null===N?L.memoizedState=N=a:N=N.next=a;return N}
function kd(){if(null===M){var a=L.alternate;a=null!==a?a.memoizedState:null}else a=M.next;var b=null===N?L.memoizedState:N.next;if(null!==b)N=b,M=a;else{if(null===a)throw Error("Rendered more hooks than during the previous render.");M=a;a={memoizedState:M.memoizedState,baseState:M.baseState,baseQueue:M.baseQueue,queue:M.queue,next:null};null===N?L.memoizedState=N=a:N=N.next=a}return N}function ld(a,b){return"function"===typeof b?b(a):b}
function md(a){var b=kd(),c=b.queue;if(null===c)throw Error("Should have a queue. This is likely a bug in React. Please file an issue.");c.lastRenderedReducer=a;var d=M,e=d.baseQueue,f=c.pending;if(null!==f){if(null!==e){var g=e.next;e.next=f.next;f.next=g}d.baseQueue=e=f;c.pending=null}if(null!==e){f=e.next;d=d.baseState;var h=g=null,k=null,l=f;do{var m=l.lane;if(($c&m)===m)null!==k&&(k=k.next={lane:0,action:l.action,hasEagerState:l.hasEagerState,eagerState:l.eagerState,next:null}),d=l.hasEagerState?
l.eagerState:a(d,l.action);else{var w={lane:m,action:l.action,hasEagerState:l.hasEagerState,eagerState:l.eagerState,next:null};null===k?(h=k=w,g=d):k=k.next=w;L.lanes|=m;J|=m}l=l.next}while(null!==l&&l!==f);null===k?g=d:k.next=h;Hb(d,b.memoizedState)||(F=!0);b.memoizedState=d;b.baseState=g;b.baseQueue=k;c.lastRenderedState=d}a=c.interleaved;if(null!==a){e=a;do f=e.lane,L.lanes|=f,J|=f,e=e.next;while(e!==a)}else null===e&&(c.lanes=0);return[b.memoizedState,c.dispatch]}
function nd(a){var b=kd(),c=b.queue;if(null===c)throw Error("Should have a queue. This is likely a bug in React. Please file an issue.");c.lastRenderedReducer=a;var d=c.dispatch,e=c.pending,f=b.memoizedState;if(null!==e){c.pending=null;var g=e=e.next;do f=a(f,g.action),g=g.next;while(g!==e);Hb(f,b.memoizedState)||(F=!0);b.memoizedState=f;null===b.baseQueue&&(b.baseState=f);c.lastRenderedState=f}return[f,d]}function od(){}
function pd(a,b){var c=L,d=kd(),e=b(),f=!Hb(d.memoizedState,e);f&&(d.memoizedState=e,F=!0);d=d.queue;qd(rd.bind(null,c,d,a),[a]);if(d.getSnapshot!==b||f||null!==N&&N.memoizedState.tag&1){c.flags|=2048;sd(9,td.bind(null,c,d,e,b),void 0,null);if(null===P)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");0!==($c&30)||ud(c,b,e)}return e}
function ud(a,b,c){a.flags|=16384;a={getSnapshot:b,value:c};b=L.updateQueue;null===b?(b={lastEffect:null,stores:null},L.updateQueue=b,b.stores=[a]):(c=b.stores,null===c?b.stores=[a]:c.push(a))}function td(a,b,c,d){b.value=c;b.getSnapshot=d;vd(b)&&wd(a)}function rd(a,b,c){return c(function(){vd(b)&&wd(a)})}function vd(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!Hb(a,c)}catch(d){return!0}}function wd(a){var b=hc(a,1);null!==b&&vc(b,a,1,-1)}
function xd(a){var b=jd();"function"===typeof a&&(a=a());b.memoizedState=b.baseState=a;a={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ld,lastRenderedState:a};b.queue=a;a=a.dispatch=yd.bind(null,L,a);return[b.memoizedState,a]}
function sd(a,b,c,d){a={tag:a,create:b,destroy:c,deps:d,next:null};b=L.updateQueue;null===b?(b={lastEffect:null,stores:null},L.updateQueue=b,b.lastEffect=a.next=a):(c=b.lastEffect,null===c?b.lastEffect=a.next=a:(d=c.next,c.next=a,a.next=d,b.lastEffect=a));return a}function zd(){return kd().memoizedState}function Ad(a,b,c,d){var e=jd();L.flags|=a;e.memoizedState=sd(1|b,c,void 0,void 0===d?null:d)}
function Bd(a,b,c,d){var e=kd();d=void 0===d?null:d;var f=void 0;if(null!==M){var g=M.memoizedState;f=g.destroy;if(null!==d&&dd(d,g.deps)){e.memoizedState=sd(b,c,f,d);return}}L.flags|=a;e.memoizedState=sd(1|b,c,f,d)}function Cd(a,b){return Ad(8390656,8,a,b)}function qd(a,b){return Bd(2048,8,a,b)}function Dd(a,b){return Bd(4,2,a,b)}function Ed(a,b){return Bd(4,4,a,b)}
function Fd(a,b){if("function"===typeof b)return a=a(),b(a),function(){b(null)};if(null!==b&&void 0!==b)return a=a(),b.current=a,function(){b.current=null}}function Gd(a,b,c){c=null!==c&&void 0!==c?c.concat([a]):null;return Bd(4,4,Fd.bind(null,b,a),c)}function Hd(){}function Id(a,b){var c=kd();b=void 0===b?null:b;var d=c.memoizedState;if(null!==d&&null!==b&&dd(b,d[1]))return d[0];c.memoizedState=[a,b];return a}
function Jd(a,b){var c=kd();b=void 0===b?null:b;var d=c.memoizedState;if(null!==d&&null!==b&&dd(b,d[1]))return d[0];a=a();c.memoizedState=[a,b];return a}function Kd(a,b,c){if(0===($c&21))return a.baseState&&(a.baseState=!1,F=!0),a.memoizedState=c;Hb(c,b)||(c=ab(),L.lanes|=c,J|=c,a.baseState=!0);return b}function Ld(a,b){var c=v;v=0!==c&&4>c?c:4;a(!0);var d=Zc.transition;Zc.transition={};try{a(!1),b()}finally{v=c,Zc.transition=d}}function Md(){return kd().memoizedState}
function Nd(a,b,c){var d=uc(a);c={lane:d,action:c,hasEagerState:!1,eagerState:null,next:null};if(Od(a))Pd(b,c);else if(c=gc(a,b,c,d),null!==c){var e=tc();vc(c,a,d,e);Qd(c,b,d)}}
function yd(a,b,c){var d=uc(a),e={lane:d,action:c,hasEagerState:!1,eagerState:null,next:null};if(Od(a))Pd(b,e);else{var f=a.alternate;if(0===a.lanes&&(null===f||0===f.lanes)&&(f=b.lastRenderedReducer,null!==f))try{var g=b.lastRenderedState,h=f(g,c);e.hasEagerState=!0;e.eagerState=h;if(Hb(h,g)){var k=b.interleaved;null===k?(e.next=e,fc(b)):(e.next=k.next,k.next=e);b.interleaved=e;return}}catch(l){}finally{}c=gc(a,b,e,d);null!==c&&(e=tc(),vc(c,a,d,e),Qd(c,b,d))}}
function Od(a){var b=a.alternate;return a===L||null!==b&&b===L}function Pd(a,b){bd=ad=!0;var c=a.pending;null===c?b.next=b:(b.next=c.next,c.next=b);a.pending=b}function Qd(a,b,c){if(0!==(c&4194240)){var d=b.lanes;d&=a.pendingLanes;c|=d;b.lanes=c;eb(a,c)}}
var id={readContext:H,useCallback:O,useContext:O,useEffect:O,useImperativeHandle:O,useInsertionEffect:O,useLayoutEffect:O,useMemo:O,useReducer:O,useRef:O,useState:O,useDebugValue:O,useDeferredValue:O,useTransition:O,useMutableSource:O,useSyncExternalStore:O,useId:O,unstable_isNewReconciler:!1},fd={readContext:H,useCallback:function(a,b){jd().memoizedState=[a,void 0===b?null:b];return a},useContext:H,useEffect:Cd,useImperativeHandle:function(a,b,c){c=null!==c&&void 0!==c?c.concat([a]):null;return Ad(4,
4,Fd.bind(null,b,a),c)},useLayoutEffect:function(a,b){return Ad(4,4,a,b)},useInsertionEffect:function(a,b){return Ad(4,2,a,b)},useMemo:function(a,b){var c=jd();b=void 0===b?null:b;a=a();c.memoizedState=[a,b];return a},useReducer:function(a,b,c){var d=jd();b=void 0!==c?c(b):b;d.memoizedState=d.baseState=b;a={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:a,lastRenderedState:b};d.queue=a;a=a.dispatch=Nd.bind(null,L,a);return[d.memoizedState,a]},useRef:function(a){var b=jd();
a={current:a};return b.memoizedState=a},useState:xd,useDebugValue:Hd,useDeferredValue:function(a){return jd().memoizedState=a},useTransition:function(){var a=xd(!1),b=a[0];a=Ld.bind(null,a[1]);jd().memoizedState=a;return[b,a]},useMutableSource:function(){},useSyncExternalStore:function(a,b){var c=L,d=jd();var e=b();if(null===P)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");0!==($c&30)||ud(c,b,e);d.memoizedState=e;var f={value:e,getSnapshot:b};d.queue=
f;Cd(rd.bind(null,c,f,a),[a]);c.flags|=2048;sd(9,td.bind(null,c,f,e,b),void 0,null);return e},useId:function(){var a=jd(),b=P.identifierPrefix,c=cd++;b=":"+b+"r"+c.toString(32)+":";return a.memoizedState=b},unstable_isNewReconciler:!1},gd={readContext:H,useCallback:Id,useContext:H,useEffect:qd,useImperativeHandle:Gd,useInsertionEffect:Dd,useLayoutEffect:Ed,useMemo:Jd,useReducer:md,useRef:zd,useState:function(){return md(ld)},useDebugValue:Hd,useDeferredValue:function(a){var b=kd();return Kd(b,M.memoizedState,
a)},useTransition:function(){var a=md(ld)[0],b=kd().memoizedState;return[a,b]},useMutableSource:od,useSyncExternalStore:pd,useId:Md,unstable_isNewReconciler:!1},hd={readContext:H,useCallback:Id,useContext:H,useEffect:qd,useImperativeHandle:Gd,useInsertionEffect:Dd,useLayoutEffect:Ed,useMemo:Jd,useReducer:nd,useRef:zd,useState:function(){return nd(ld)},useDebugValue:Hd,useDeferredValue:function(a){var b=kd();return null===M?b.memoizedState=a:Kd(b,M.memoizedState,a)},useTransition:function(){var a=
nd(ld)[0],b=kd().memoizedState;return[a,b]},useMutableSource:od,useSyncExternalStore:pd,useId:Md,unstable_isNewReconciler:!1};function Rd(a,b){try{var c="",d=b;do c+=Vb(d),d=d.return;while(d);var e=c}catch(f){e="\nError generating stack: "+f.message+"\n"+f.stack}return{value:a,source:b,stack:e,digest:null}}function Sd(a,b,c){return{value:a,source:null,stack:null!=c?c:null,digest:null!=b?b:null}}function Td(a,b){try{console.error(b.value)}catch(c){setTimeout(function(){throw c;})}}
var Ud="function"===typeof WeakMap?WeakMap:Map;function Vd(a,b,c){c=lc(-1,c);c.tag=3;c.payload={element:null};var d=b.value;c.callback=function(){Wd||(Wd=!0,Xd=d);Td(a,b)};return c}
function Yd(a,b,c){c=lc(-1,c);c.tag=3;var d=a.type.getDerivedStateFromError;if("function"===typeof d){var e=b.value;c.payload=function(){return d(e)};c.callback=function(){Td(a,b)}}var f=a.stateNode;null!==f&&"function"===typeof f.componentDidCatch&&(c.callback=function(){Td(a,b);"function"!==typeof d&&(null===Zd?Zd=new Set([this]):Zd.add(this));var c=b.stack;this.componentDidCatch(b.value,{componentStack:null!==c?c:""})});return c}
function $d(a,b,c){var d=a.pingCache;if(null===d){d=a.pingCache=new Ud;var e=new Set;d.set(b,e)}else e=d.get(b),void 0===e&&(e=new Set,d.set(b,e));e.has(c)||(e.add(c),a=ae.bind(null,a,b,c),b.then(a,a))}var be=ha.ReactCurrentOwner,F=!1;function Q(a,b,c,d){b.child=null===a?Lc(b,null,c,d):Kc(b,a.child,c,d)}
function ce(a,b,c,d,e){c=c.render;var f=b.ref;dc(b,e);d=ed(a,b,c,d,f,e);if(null!==a&&!F)return b.updateQueue=a.updateQueue,b.flags&=-2053,a.lanes&=~e,de(a,b,e);b.flags|=1;Q(a,b,d,e);return b.child}
function ee(a,b,c,d,e){if(null===a){var f=c.type;if("function"===typeof f&&!fe(f)&&void 0===f.defaultProps&&null===c.compare&&void 0===c.defaultProps)return b.tag=15,b.type=f,ge(a,b,f,d,e);a=Hc(c.type,null,d,b,b.mode,e);a.ref=b.ref;a.return=b;return b.child=a}f=a.child;if(0===(a.lanes&e)){var g=f.memoizedProps;c=c.compare;c=null!==c?c:Ub;if(c(g,d)&&a.ref===b.ref)return de(a,b,e)}b.flags|=1;a=Fc(f,d);a.ref=b.ref;a.return=b;return b.child=a}
function ge(a,b,c,d,e){if(null!==a){var f=a.memoizedProps;if(Ub(f,d)&&a.ref===b.ref)if(F=!1,b.pendingProps=d=f,0!==(a.lanes&e))0!==(a.flags&131072)&&(F=!0);else return b.lanes=a.lanes,de(a,b,e)}return he(a,b,c,d,e)}
function ie(a,b,c){var d=b.pendingProps,e=d.children,f=null!==a?a.memoizedState:null;if("hidden"===d.mode)if(0===(b.mode&1))b.memoizedState={baseLanes:0,cachePool:null,transitions:null},y(je,R),R|=c;else{if(0===(c&1073741824))return a=null!==f?f.baseLanes|c:c,b.lanes=b.childLanes=1073741824,b.memoizedState={baseLanes:a,cachePool:null,transitions:null},b.updateQueue=null,y(je,R),R|=a,null;b.memoizedState={baseLanes:0,cachePool:null,transitions:null};d=null!==f?f.baseLanes:c;y(je,R);R|=d}else null!==
f?(d=f.baseLanes|c,b.memoizedState=null):d=c,y(je,R),R|=d;Q(a,b,e,c);return b.child}function ke(a,b){var c=b.ref;if(null===a&&null!==c||null!==a&&a.ref!==c)b.flags|=512}function he(a,b,c,d,e){var f=D(c)?zb:z.current;f=Ab(b,f);dc(b,e);c=ed(a,b,c,d,f,e);if(null!==a&&!F)return b.updateQueue=a.updateQueue,b.flags&=-2053,a.lanes&=~e,de(a,b,e);b.flags|=1;Q(a,b,c,e);return b.child}
function le(a,b,c,d,e){if(D(c)){var f=!0;Eb(b)}else f=!1;dc(b,e);if(null===b.stateNode)me(a,b),yc(b,c,d),Ac(b,c,d,e),d=!0;else if(null===a){var g=b.stateNode,h=b.memoizedProps;g.props=h;var k=g.context,l=c.contextType;"object"===typeof l&&null!==l?l=H(l):(l=D(c)?zb:z.current,l=Ab(b,l));var m=c.getDerivedStateFromProps,w="function"===typeof m||"function"===typeof g.getSnapshotBeforeUpdate;w||"function"!==typeof g.UNSAFE_componentWillReceiveProps&&"function"!==typeof g.componentWillReceiveProps||(h!==
d||k!==l)&&zc(b,g,d,l);ic=!1;var p=b.memoizedState;g.state=p;pc(b,d,g,e);k=b.memoizedState;h!==d||p!==k||C.current||ic?("function"===typeof m&&(sc(b,c,m,d),k=b.memoizedState),(h=ic||xc(b,c,h,d,p,k,l))?(w||"function"!==typeof g.UNSAFE_componentWillMount&&"function"!==typeof g.componentWillMount||("function"===typeof g.componentWillMount&&g.componentWillMount(),"function"===typeof g.UNSAFE_componentWillMount&&g.UNSAFE_componentWillMount()),"function"===typeof g.componentDidMount&&(b.flags|=4)):("function"===
typeof g.componentDidMount&&(b.flags|=4),b.memoizedProps=d,b.memoizedState=k),g.props=d,g.state=k,g.context=l,d=h):("function"===typeof g.componentDidMount&&(b.flags|=4),d=!1)}else{g=b.stateNode;kc(a,b);h=b.memoizedProps;l=b.type===b.elementType?h:Wb(b.type,h);g.props=l;w=b.pendingProps;p=g.context;k=c.contextType;"object"===typeof k&&null!==k?k=H(k):(k=D(c)?zb:z.current,k=Ab(b,k));var B=c.getDerivedStateFromProps;(m="function"===typeof B||"function"===typeof g.getSnapshotBeforeUpdate)||"function"!==
typeof g.UNSAFE_componentWillReceiveProps&&"function"!==typeof g.componentWillReceiveProps||(h!==w||p!==k)&&zc(b,g,d,k);ic=!1;p=b.memoizedState;g.state=p;pc(b,d,g,e);var r=b.memoizedState;h!==w||p!==r||C.current||ic?("function"===typeof B&&(sc(b,c,B,d),r=b.memoizedState),(l=ic||xc(b,c,l,d,p,r,k)||!1)?(m||"function"!==typeof g.UNSAFE_componentWillUpdate&&"function"!==typeof g.componentWillUpdate||("function"===typeof g.componentWillUpdate&&g.componentWillUpdate(d,r,k),"function"===typeof g.UNSAFE_componentWillUpdate&&
g.UNSAFE_componentWillUpdate(d,r,k)),"function"===typeof g.componentDidUpdate&&(b.flags|=4),"function"===typeof g.getSnapshotBeforeUpdate&&(b.flags|=1024)):("function"!==typeof g.componentDidUpdate||h===a.memoizedProps&&p===a.memoizedState||(b.flags|=4),"function"!==typeof g.getSnapshotBeforeUpdate||h===a.memoizedProps&&p===a.memoizedState||(b.flags|=1024),b.memoizedProps=d,b.memoizedState=r),g.props=d,g.state=r,g.context=k,d=l):("function"!==typeof g.componentDidUpdate||h===a.memoizedProps&&p===
a.memoizedState||(b.flags|=4),"function"!==typeof g.getSnapshotBeforeUpdate||h===a.memoizedProps&&p===a.memoizedState||(b.flags|=1024),d=!1)}return ne(a,b,c,d,f,e)}
function ne(a,b,c,d,e,f){ke(a,b);var g=0!==(b.flags&128);if(!d&&!g)return e&&Fb(b,c,!1),de(a,b,f);d=b.stateNode;be.current=b;var h=g&&"function"!==typeof c.getDerivedStateFromError?null:d.render();b.flags|=1;null!==a&&g?(b.child=Kc(b,a.child,null,f),b.child=Kc(b,null,h,f)):Q(a,b,h,f);b.memoizedState=d.state;e&&Fb(b,c,!0);return b.child}function oe(a){var b=a.stateNode;b.pendingContext?Cb(a,b.pendingContext,b.pendingContext!==b.context):b.context&&Cb(a,b.context,!1);Rc(a,b.containerInfo)}
var pe={dehydrated:null,treeContext:null,retryLane:0};function qe(a){return{baseLanes:a,cachePool:null,transitions:null}}
function re(a,b,c){var d=b.pendingProps,e=K.current,f=!1,g=0!==(b.flags&128),h;(h=g)||(h=null!==a&&null===a.memoizedState?!1:0!==(e&2));if(h)f=!0,b.flags&=-129;else if(null===a||null!==a.memoizedState)e|=1;y(K,e&1);if(null===a){a=b.memoizedState;if(null!==a&&null!==a.dehydrated)return 0===(b.mode&1)?b.lanes=1:gb()?b.lanes=8:b.lanes=1073741824,null;g=d.children;a=d.fallback;return f?(d=b.mode,f=b.child,g={mode:"hidden",children:g},0===(d&1)&&null!==f?(f.childLanes=0,f.pendingProps=g):f=se(g,d,0,null),
a=Jc(a,d,c,null),f.return=b,a.return=b,f.sibling=a,b.child=f,b.child.memoizedState=qe(c),b.memoizedState=pe,a):te(b,g)}e=a.memoizedState;if(null!==e&&(h=e.dehydrated,null!==h))return ue(a,b,g,d,h,e,c);if(f){f=d.fallback;g=b.mode;e=a.child;h=e.sibling;var k={mode:"hidden",children:d.children};0===(g&1)&&b.child!==e?(d=b.child,d.childLanes=0,d.pendingProps=k,b.deletions=null):(d=Fc(e,k),d.subtreeFlags=e.subtreeFlags&14680064);null!==h?f=Fc(h,f):(f=Jc(f,g,c,null),f.flags|=2);f.return=b;d.return=b;d.sibling=
f;b.child=d;d=f;f=b.child;g=a.child.memoizedState;g=null===g?qe(c):{baseLanes:g.baseLanes|c,cachePool:null,transitions:g.transitions};f.memoizedState=g;f.childLanes=a.childLanes&~c;b.memoizedState=pe;return d}f=a.child;a=f.sibling;d=Fc(f,{mode:"visible",children:d.children});0===(b.mode&1)&&(d.lanes=c);d.return=b;d.sibling=null;null!==a&&(c=b.deletions,null===c?(b.deletions=[a],b.flags|=16):c.push(a));b.child=d;b.memoizedState=null;return d}
function te(a,b){b=se({mode:"visible",children:b},a.mode,0,null);b.return=a;return a.child=b}function ve(a,b,c,d){null!==d&&(null===Sb?Sb=[d]:Sb.push(d));Kc(b,a.child,null,c);a=te(b,b.pendingProps.children);a.flags|=2;b.memoizedState=null;return a}
function ue(a,b,c,d,e,f,g){if(c){if(b.flags&256)return b.flags&=-257,f=Sd(Error("There was an error while hydrating this Suspense boundary. Switched to client rendering.")),ve(a,b,g,f);if(null!==b.memoizedState)return b.child=a.child,b.flags|=128,null;f=d.fallback;c=b.mode;d=se({mode:"visible",children:d.children},c,0,null);f=Jc(f,c,g,null);f.flags|=2;d.return=b;f.return=b;d.sibling=f;b.child=d;0!==(b.mode&1)&&Kc(b,a.child,null,g);b.child.memoizedState=qe(g);b.memoizedState=pe;return f}if(0===(b.mode&
1))return ve(a,b,g,null);if(gb())return f=gb().digest,f=Sd(Error("The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering."),f,void 0),ve(a,b,g,f);c=0!==(g&a.childLanes);if(F||c){d=P;if(null!==d){switch(g&-g){case 4:c=2;break;case 16:c=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:c=
32;break;case 536870912:c=268435456;break;default:c=0}c=0!==(c&(d.suspendedLanes|g))?0:c;0!==c&&c!==f.retryLane&&(f.retryLane=c,hc(a,c),vc(d,a,c,-1))}we();f=Sd(Error("This Suspense boundary received an update before it finished hydrating. This caused the boundary to switch to client rendering. The usual way to fix this is to wrap the original update in startTransition."));return ve(a,b,g,f)}if(gb())return b.flags|=128,b.child=a.child,xe.bind(null,a),gb(),null;a=te(b,d.children);a.flags|=4096;return a}
function ye(a,b,c){a.lanes|=b;var d=a.alternate;null!==d&&(d.lanes|=b);cc(a.return,b,c)}function ze(a,b,c,d,e){var f=a.memoizedState;null===f?a.memoizedState={isBackwards:b,rendering:null,renderingStartTime:0,last:d,tail:c,tailMode:e}:(f.isBackwards=b,f.rendering=null,f.renderingStartTime=0,f.last=d,f.tail=c,f.tailMode=e)}
function Ae(a,b,c){var d=b.pendingProps,e=d.revealOrder,f=d.tail;Q(a,b,d.children,c);d=K.current;if(0!==(d&2))d=d&1|2,b.flags|=128;else{if(null!==a&&0!==(a.flags&128))a:for(a=b.child;null!==a;){if(13===a.tag)null!==a.memoizedState&&ye(a,c,b);else if(19===a.tag)ye(a,c,b);else if(null!==a.child){a.child.return=a;a=a.child;continue}if(a===b)break a;for(;null===a.sibling;){if(null===a.return||a.return===b)break a;a=a.return}a.sibling.return=a.return;a=a.sibling}d&=1}y(K,d);if(0===(b.mode&1))b.memoizedState=
null;else switch(e){case "forwards":c=b.child;for(e=null;null!==c;)a=c.alternate,null!==a&&null===Vc(a)&&(e=c),c=c.sibling;c=e;null===c?(e=b.child,b.child=null):(e=c.sibling,c.sibling=null);ze(b,!1,e,c,f);break;case "backwards":c=null;e=b.child;for(b.child=null;null!==e;){a=e.alternate;if(null!==a&&null===Vc(a)){b.child=e;break}a=e.sibling;e.sibling=c;c=e;e=a}ze(b,!0,c,null,f);break;case "together":ze(b,!1,null,null,void 0);break;default:b.memoizedState=null}return b.child}
function me(a,b){0===(b.mode&1)&&null!==a&&(a.alternate=null,b.alternate=null,b.flags|=2)}function de(a,b,c){null!==a&&(b.dependencies=a.dependencies);J|=b.lanes;if(0===(c&b.childLanes))return null;if(null!==a&&b.child!==a.child)throw Error("Resuming work not yet implemented.");if(null!==b.child){a=b.child;c=Fc(a,a.pendingProps);b.child=c;for(c.return=b;null!==a.sibling;)a=a.sibling,c=c.sibling=Fc(a,a.pendingProps),c.return=b;c.sibling=null}return b.child}
function Be(a,b,c){switch(b.tag){case 3:oe(b);break;case 5:Tc(b);break;case 1:D(b.type)&&Eb(b);break;case 4:Rc(b,b.stateNode.containerInfo);break;case 10:var d=b.type._context,e=b.memoizedProps.value;y(Xb,d._currentValue2);d._currentValue2=e;break;case 13:d=b.memoizedState;if(null!==d){if(null!==d.dehydrated)return y(K,K.current&1),b.flags|=128,null;if(0!==(c&b.child.childLanes))return re(a,b,c);y(K,K.current&1);a=de(a,b,c);return null!==a?a.sibling:null}y(K,K.current&1);break;case 19:d=0!==(c&b.childLanes);
if(0!==(a.flags&128)){if(d)return Ae(a,b,c);b.flags|=128}e=b.memoizedState;null!==e&&(e.rendering=null,e.tail=null,e.lastEffect=null);y(K,K.current);if(d)break;else return null;case 22:case 23:return b.lanes=0,ie(a,b,c)}return de(a,b,c)}var Ce,De,Ee,Fe;
Ce=function(a,b){for(var c=b.child;null!==c;){if(5===c.tag||6===c.tag){var d=a,e=c.stateNode,f=d.children.indexOf(e);-1!==f&&d.children.splice(f,1);d.children.push(e)}else if(4!==c.tag&&null!==c.child){c.child.return=c;c=c.child;continue}if(c===b)break;for(;null===c.sibling;){if(null===c.return||c.return===b)return;c=c.return}c.sibling.return=c.return;c=c.sibling}};De=function(){};Ee=function(a,b,c,d){a.memoizedProps!==d&&(Qc(Nc.current),b.updateQueue=jb)&&(b.flags|=4)};
Fe=function(a,b,c,d){c!==d&&(b.flags|=4)};function Ge(a,b){switch(a.tailMode){case "hidden":b=a.tail;for(var c=null;null!==b;)null!==b.alternate&&(c=b),b=b.sibling;null===c?a.tail=null:c.sibling=null;break;case "collapsed":c=a.tail;for(var d=null;null!==c;)null!==c.alternate&&(d=c),c=c.sibling;null===d?b||null===a.tail?a.tail=null:a.tail.sibling=null:d.sibling=null}}
function S(a){var b=null!==a.alternate&&a.alternate.child===a.child,c=0,d=0;if(b)for(var e=a.child;null!==e;)c|=e.lanes|e.childLanes,d|=e.subtreeFlags&14680064,d|=e.flags&14680064,e.return=a,e=e.sibling;else for(e=a.child;null!==e;)c|=e.lanes|e.childLanes,d|=e.subtreeFlags,d|=e.flags,e.return=a,e=e.sibling;a.subtreeFlags|=d;a.childLanes=c;return b}
function He(a,b,c){var d=b.pendingProps;Rb(b);switch(b.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return S(b),null;case 1:return D(b.type)&&Bb(),S(b),null;case 3:return c=b.stateNode,Sc(),x(C),x(z),Xc(),c.pendingContext&&(c.context=c.pendingContext,c.pendingContext=null),null!==a&&null!==a.child||null===a||a.memoizedState.isDehydrated&&0===(b.flags&256)||(b.flags|=1024,null!==Sb&&(Ie(Sb),Sb=null)),De(a,b),S(b),null;case 5:Uc(b);c=Qc(Pc.current);var e=b.type;if(null!==
a&&null!=b.stateNode)Ee(a,b,e,d,c),a.ref!==b.ref&&(b.flags|=512);else{if(!d){if(null===b.stateNode)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");S(b);return null}Qc(Nc.current);a={type:e,props:d,isHidden:!1,children:[],internalInstanceHandle:b,rootContainerInstance:c,tag:"INSTANCE"};Ce(a,b,!1,!1);b.stateNode=a;null!==b.ref&&(b.flags|=512)}S(b);return null;case 6:if(a&&null!=b.stateNode)Fe(a,b,a.memoizedProps,d);else{if("string"!==
typeof d&&null===b.stateNode)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");Qc(Pc.current);Qc(Nc.current);b.stateNode={text:d,isHidden:!1,tag:"TEXT"}}S(b);return null;case 13:x(K);d=b.memoizedState;if(null===a||null!==a.memoizedState&&null!==a.memoizedState.dehydrated){if(null!==d&&null!==d.dehydrated){if(null===a){throw Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");
throw Error("Expected prepareToHydrateHostSuspenseInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");}0===(b.flags&128)&&(b.memoizedState=null);b.flags|=4;S(b);e=!1}else null!==Sb&&(Ie(Sb),Sb=null),e=!0;if(!e)return b.flags&65536?b:null}if(0!==(b.flags&128))return b.lanes=c,b;c=null!==d;c!==(null!==a&&null!==a.memoizedState)&&c&&(b.child.flags|=8192,0!==(b.mode&1)&&(null===a||0!==(K.current&1)?0===T&&(T=3):we()));null!==b.updateQueue&&(b.flags|=4);
S(b);return null;case 4:return Sc(),De(a,b),S(b),null;case 10:return bc(b.type._context),S(b),null;case 17:return D(b.type)&&Bb(),S(b),null;case 19:x(K);e=b.memoizedState;if(null===e)return S(b),null;d=0!==(b.flags&128);var f=e.rendering;if(null===f)if(d)Ge(e,!1);else{if(0!==T||null!==a&&0!==(a.flags&128))for(a=b.child;null!==a;){f=Vc(a);if(null!==f){b.flags|=128;Ge(e,!1);a=f.updateQueue;null!==a&&(b.updateQueue=a,b.flags|=4);b.subtreeFlags=0;a=c;for(c=b.child;null!==c;)d=c,e=a,d.flags&=14680066,
f=d.alternate,null===f?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=f.childLanes,d.lanes=f.lanes,d.child=f.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=f.memoizedProps,d.memoizedState=f.memoizedState,d.updateQueue=f.updateQueue,d.type=f.type,e=f.dependencies,d.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),c=c.sibling;y(K,K.current&1|2);return b.child}a=
a.sibling}null!==e.tail&&q()>Je&&(b.flags|=128,d=!0,Ge(e,!1),b.lanes=4194304)}else{if(!d)if(a=Vc(f),null!==a){if(b.flags|=128,d=!0,a=a.updateQueue,null!==a&&(b.updateQueue=a,b.flags|=4),Ge(e,!0),null===e.tail&&"hidden"===e.tailMode&&!f.alternate)return S(b),null}else 2*q()-e.renderingStartTime>Je&&1073741824!==c&&(b.flags|=128,d=!0,Ge(e,!1),b.lanes=4194304);e.isBackwards?(f.sibling=b.child,b.child=f):(a=e.last,null!==a?a.sibling=f:b.child=f,e.last=f)}if(null!==e.tail)return b=e.tail,e.rendering=b,
e.tail=b.sibling,e.renderingStartTime=q(),b.sibling=null,a=K.current,y(K,d?a&1|2:a&1),b;S(b);return null;case 22:case 23:return Ke(),c=null!==b.memoizedState,null!==a&&null!==a.memoizedState!==c&&(b.flags|=8192),c&&0!==(b.mode&1)?0!==(R&1073741824)&&(S(b),b.subtreeFlags&6&&(b.flags|=8192)):S(b),null;case 24:return null;case 25:return null}throw Error("Unknown unit of work tag ("+b.tag+"). This error is likely caused by a bug in React. Please file an issue.");}
function Le(a,b){Rb(b);switch(b.tag){case 1:return D(b.type)&&Bb(),a=b.flags,a&65536?(b.flags=a&-65537|128,b):null;case 3:return Sc(),x(C),x(z),Xc(),a=b.flags,0!==(a&65536)&&0===(a&128)?(b.flags=a&-65537|128,b):null;case 5:return Uc(b),null;case 13:x(K);a=b.memoizedState;if(null!==a&&null!==a.dehydrated&&null===b.alternate)throw Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");a=b.flags;return a&65536?(b.flags=a&-65537|128,b):null;case 19:return x(K),
null;case 4:return Sc(),null;case 10:return bc(b.type._context),null;case 22:case 23:return Ke(),null;case 24:return null;default:return null}}var Me="function"===typeof WeakSet?WeakSet:Set,U=null;function Ne(a,b){var c=a.ref;if(null!==c)if("function"===typeof c)try{c(null)}catch(d){V(a,b,d)}else c.current=null}function Oe(a,b,c){try{c()}catch(d){V(a,b,d)}}var Pe=!1;
function Qe(a,b){for(U=b;null!==U;)if(a=U,b=a.child,0!==(a.subtreeFlags&1028)&&null!==b)b.return=a,U=b;else for(;null!==U;){a=U;try{var c=a.alternate;if(0!==(a.flags&1024))switch(a.tag){case 0:case 11:case 15:break;case 1:if(null!==c){var d=c.memoizedProps,e=c.memoizedState,f=a.stateNode,g=f.getSnapshotBeforeUpdate(a.elementType===a.type?d:Wb(a.type,d),e);f.__reactInternalSnapshotBeforeUpdate=g}break;case 3:a.stateNode.containerInfo.children.splice(0);break;case 5:case 6:case 4:case 17:break;default:throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.");
}}catch(h){V(a,a.return,h)}b=a.sibling;if(null!==b){b.return=a.return;U=b;break}U=a.return}c=Pe;Pe=!1;return c}function Re(a,b,c){var d=b.updateQueue;d=null!==d?d.lastEffect:null;if(null!==d){var e=d=d.next;do{if((e.tag&a)===a){var f=e.destroy;e.destroy=void 0;void 0!==f&&Oe(b,c,f)}e=e.next}while(e!==d)}}function Se(a,b){b=b.updateQueue;b=null!==b?b.lastEffect:null;if(null!==b){var c=b=b.next;do{if((c.tag&a)===a){var d=c.create;c.destroy=d()}c=c.next}while(c!==b)}}
function Te(a){var b=a.alternate;null!==b&&(a.alternate=null,Te(b));a.child=null;a.deletions=null;a.sibling=null;a.stateNode=null;a.return=null;a.dependencies=null;a.memoizedProps=null;a.memoizedState=null;a.pendingProps=null;a.stateNode=null;a.updateQueue=null}function Ue(a){return 5===a.tag||3===a.tag||4===a.tag}
function Ve(a){a:for(;;){for(;null===a.sibling;){if(null===a.return||Ue(a.return))return null;a=a.return}a.sibling.return=a.return;for(a=a.sibling;5!==a.tag&&6!==a.tag&&18!==a.tag;){if(a.flags&2)continue a;if(null===a.child||4===a.tag)continue a;else a.child.return=a,a=a.child}if(!(a.flags&2))return a.stateNode}}function We(a,b,c){var d=a.tag;if(5===d||6===d)a=a.stateNode,b?nb(c,a,b):mb(c,a);else if(4!==d&&(a=a.child,null!==a))for(We(a,b,c),a=a.sibling;null!==a;)We(a,b,c),a=a.sibling}
function Xe(a,b,c){var d=a.tag;if(5===d||6===d)a=a.stateNode,b?nb(c,a,b):mb(c,a);else if(4!==d&&(a=a.child,null!==a))for(Xe(a,b,c),a=a.sibling;null!==a;)Xe(a,b,c),a=a.sibling}var W=null;function Ye(a,b,c){for(c=c.child;null!==c;)Ze(a,b,c),c=c.sibling}
function Ze(a,b,c){if(Oa&&"function"===typeof Oa.onCommitFiberUnmount)try{Oa.onCommitFiberUnmount(Na,c)}catch(h){}switch(c.tag){case 5:Ne(c,b);case 6:var d=W;W=null;Ye(a,b,c);W=d;null!==W&&(a=W,c=a.children.indexOf(c.stateNode),a.children.splice(c,1));break;case 18:null!==W&&gb(W,c.stateNode);break;case 4:d=W;W=c.stateNode.containerInfo;Ye(a,b,c);W=d;break;case 0:case 11:case 14:case 15:d=c.updateQueue;if(null!==d&&(d=d.lastEffect,null!==d)){var e=d=d.next;do{var f=e,g=f.destroy;f=f.tag;void 0!==
g&&(0!==(f&2)?Oe(c,b,g):0!==(f&4)&&Oe(c,b,g));e=e.next}while(e!==d)}Ye(a,b,c);break;case 1:Ne(c,b);d=c.stateNode;if("function"===typeof d.componentWillUnmount)try{d.props=c.memoizedProps,d.state=c.memoizedState,d.componentWillUnmount()}catch(h){V(c,b,h)}Ye(a,b,c);break;case 21:Ye(a,b,c);break;case 22:Ye(a,b,c);break;default:Ye(a,b,c)}}
function $e(a){var b=a.updateQueue;if(null!==b){a.updateQueue=null;var c=a.stateNode;null===c&&(c=a.stateNode=new Me);b.forEach(function(b){var d=af.bind(null,a,b);c.has(b)||(c.add(b),b.then(d,d))})}}
function bf(a,b){var c=b.deletions;if(null!==c)for(var d=0;d<c.length;d++){var e=c[d];try{var f=a,g=b,h=g;a:for(;null!==h;){switch(h.tag){case 5:W=h.stateNode;break a;case 3:W=h.stateNode.containerInfo;break a;case 4:W=h.stateNode.containerInfo;break a}h=h.return}if(null===W)throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");Ze(f,g,e);W=null;var k=e.alternate;null!==k&&(k.return=null);e.return=null}catch(l){V(e,b,l)}}if(b.subtreeFlags&
12854)for(b=b.child;null!==b;)cf(b,a),b=b.sibling}
function cf(a,b){var c=a.alternate,d=a.flags;switch(a.tag){case 0:case 11:case 14:case 15:bf(b,a);df(a);if(d&4){try{Re(3,a,a.return),Se(3,a)}catch(f){V(a,a.return,f)}try{Re(5,a,a.return)}catch(f){V(a,a.return,f)}}break;case 1:bf(b,a);df(a);d&512&&null!==c&&Ne(c,c.return);break;case 5:bf(b,a);df(a);d&512&&null!==c&&Ne(c,c.return);if(d&4){var e=a.stateNode;if(null!=e&&(d=a.memoizedProps,b=a.type,c=a.updateQueue,a.updateQueue=null,null!==c))try{e.type=b,e.props=d}catch(f){V(a,a.return,f)}}break;case 6:bf(b,
a);df(a);if(d&4){if(null===a.stateNode)throw Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");e=a.stateNode;d=a.memoizedProps;try{e.text=d}catch(f){V(a,a.return,f)}}break;case 3:bf(b,a);df(a);break;case 4:bf(b,a);df(a);break;case 13:bf(b,a);df(a);e=a.child;e.flags&8192&&(b=null!==e.memoizedState,e.stateNode.isHidden=b,!b||null!==e.alternate&&null!==e.alternate.memoizedState||(ef=q()));d&4&&$e(a);break;case 22:bf(b,a);df(a);if(d&
8192)a:for(d=null!==a.memoizedState,a.stateNode.isHidden=d,b=null,c=a;;){if(5===c.tag){if(null===b){b=c;try{e=c.stateNode,d?e.isHidden=!0:c.stateNode.isHidden=!1}catch(f){V(a,a.return,f)}}}else if(6===c.tag){if(null===b)try{c.stateNode.isHidden=d?!0:!1}catch(f){V(a,a.return,f)}}else if((22!==c.tag&&23!==c.tag||null===c.memoizedState||c===a)&&null!==c.child){c.child.return=c;c=c.child;continue}if(c===a)break a;for(;null===c.sibling;){if(null===c.return||c.return===a)break a;b===c&&(b=null);c=c.return}b===
c&&(b=null);c.sibling.return=c.return;c=c.sibling}break;case 19:bf(b,a);df(a);d&4&&$e(a);break;case 21:break;default:bf(b,a),df(a)}}
function df(a){var b=a.flags;if(b&2){try{a:{for(var c=a.return;null!==c;){if(Ue(c)){var d=c;break a}c=c.return}throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");}switch(d.tag){case 5:var e=d.stateNode;d.flags&32&&(d.flags&=-33);var f=Ve(a);Xe(a,f,e);break;case 3:case 4:var g=d.stateNode.containerInfo,h=Ve(a);We(a,h,g);break;default:throw Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.");
}}catch(k){V(a,a.return,k)}a.flags&=-3}b&4096&&(a.flags&=-4097)}
function ff(a){for(U=a;null!==U;){var b=U,c=b.child;if(0!==(b.subtreeFlags&8772)&&null!==c)c.return=b,U=c;else for(b=a;null!==U;){c=U;if(0!==(c.flags&8772)){var d=c.alternate;try{if(0!==(c.flags&8772))switch(c.tag){case 0:case 11:case 15:Se(5,c);break;case 1:var e=c.stateNode;if(c.flags&4)if(null===d)e.componentDidMount();else{var f=c.elementType===c.type?d.memoizedProps:Wb(c.type,d.memoizedProps);e.componentDidUpdate(f,d.memoizedState,e.__reactInternalSnapshotBeforeUpdate)}var g=c.updateQueue;null!==
g&&qc(c,g,e);break;case 3:var h=c.updateQueue;if(null!==h){d=null;if(null!==c.child)switch(c.child.tag){case 5:d=lb(c.child.stateNode);break;case 1:d=c.child.stateNode}qc(c,h,d)}break;case 5:break;case 6:break;case 4:break;case 12:break;case 13:break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.");}if(c.flags&512){d=void 0;var k=c.ref;if(null!==k){var l=
c.stateNode;switch(c.tag){case 5:d=lb(l);break;default:d=l}"function"===typeof k?k(d):k.current=d}}}catch(m){V(c,c.return,m)}}if(c===b){U=null;break}d=c.sibling;if(null!==d){d.return=c.return;U=d;break}U=c.return}}}var gf=Math.ceil,hf=ha.ReactCurrentDispatcher,jf=ha.ReactCurrentOwner,kf=ha.ReactCurrentBatchConfig,I=0,P=null,X=null,Y=0,R=0,je=xb(0),T=0,lf=null,J=0,mf=0,nf=0,of=null,Z=null,ef=0,Je=Infinity,pf=null,Wd=!1,Xd=null,Zd=null,qf=!1,rf=null,sf=0,tf=0,uf=null,vf=-1,wf=0;
function tc(){return 0!==(I&6)?q():-1!==vf?vf:vf=q()}function uc(a){if(0===(a.mode&1))return 1;if(0!==(I&2)&&0!==Y)return Y&-Y;if(null!==Tb.transition)return 0===wf&&(wf=ab()),wf;a=v;return 0!==a?a:16}
function vc(a,b,c,d){if(50<tf)throw tf=0,uf=null,Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");cb(a,c,d);if(0===(I&2)||a!==P)a===P&&(0===(I&2)&&(mf|=c),4===T&&xf(a,Y)),yf(a,d),1===c&&0===I&&0===(b.mode&1)&&(Je=q()+500,Ib&&Kb())}
function yf(a,b){for(var c=a.callbackNode,d=a.suspendedLanes,e=a.pingedLanes,f=a.expirationTimes,g=a.pendingLanes;0<g;){var h=31-Ra(g),k=1<<h,l=f[h];if(-1===l){if(0===(k&d)||0!==(k&e))f[h]=Za(k,b)}else l<=b&&(a.expiredLanes|=k);g&=~k}d=Xa(a,a===P?Y:0);if(0===d)null!==c&&Fa(c),a.callbackNode=null,a.callbackPriority=0;else if(b=d&-d,a.callbackPriority!==b){null!=c&&Fa(c);if(1===b)0===a.tag?(c=zf.bind(null,a),Ib=!0,null===E?E=[c]:E.push(c)):(c=zf.bind(null,a),null===E?E=[c]:E.push(c)),Ea(Ia,Kb),c=null;
else{switch(fb(d)){case 1:c=Ia;break;case 4:c=Ja;break;case 16:c=La;break;case 536870912:c=Ma;break;default:c=La}c=Af(c,Bf.bind(null,a))}a.callbackPriority=b;a.callbackNode=c}}
function Bf(a,b){vf=-1;wf=0;if(0!==(I&6))throw Error("Should not already be working.");var c=a.callbackNode;if(Cf()&&a.callbackNode!==c)return null;var d=Xa(a,a===P?Y:0);if(0===d)return null;if(0!==(d&30)||0!==(d&a.expiredLanes)||b)b=Df(a,d);else{b=d;var e=I;I|=2;var f=Ef();if(P!==a||Y!==b)pf=null,Je=q()+500,Ff(a,b);do try{Gf();break}catch(h){Hf(a,h)}while(1);ac();hf.current=f;I=e;null!==X?b=0:(P=null,Y=0,b=T)}if(0!==b){2===b&&(e=$a(a),0!==e&&(d=e,b=Kf(a,e)));if(1===b)throw c=lf,Ff(a,0),xf(a,d),yf(a,
q()),c;if(6===b)xf(a,d);else{e=a.current.alternate;if(0===(d&30)&&!Lf(e)&&(b=Df(a,d),2===b&&(f=$a(a),0!==f&&(d=f,b=Kf(a,f))),1===b))throw c=lf,Ff(a,0),xf(a,d),yf(a,q()),c;a.finishedWork=e;a.finishedLanes=d;switch(b){case 0:case 1:throw Error("Root did not complete. This is a bug in React.");case 2:Mf(a,Z,pf);break;case 3:xf(a,d);if((d&130023424)===d&&(b=ef+500-q(),10<b)){if(0!==Xa(a,0))break;e=a.suspendedLanes;if((e&d)!==d){tc();a.pingedLanes|=a.suspendedLanes&e;break}a.timeoutHandle=ob(Mf.bind(null,
a,Z,pf),b);break}Mf(a,Z,pf);break;case 4:xf(a,d);if((d&4194240)===d)break;b=a.eventTimes;for(e=-1;0<d;){var g=31-Ra(d);f=1<<g;g=b[g];g>e&&(e=g);d&=~f}d=e;d=q()-d;d=(120>d?120:480>d?480:1080>d?1080:1920>d?1920:3E3>d?3E3:4320>d?4320:1960*gf(d/1960))-d;if(10<d){a.timeoutHandle=ob(Mf.bind(null,a,Z,pf),d);break}Mf(a,Z,pf);break;case 5:Mf(a,Z,pf);break;default:throw Error("Unknown root exit status.");}}}yf(a,q());return a.callbackNode===c?Bf.bind(null,a):null}
function Kf(a,b){var c=of;a.current.memoizedState.isDehydrated&&(Ff(a,b).flags|=256);a=Df(a,b);2!==a&&(b=Z,Z=c,null!==b&&Ie(b));return a}function Ie(a){null===Z?Z=a:Z.push.apply(Z,a)}
function Lf(a){for(var b=a;;){if(b.flags&16384){var c=b.updateQueue;if(null!==c&&(c=c.stores,null!==c))for(var d=0;d<c.length;d++){var e=c[d],f=e.getSnapshot;e=e.value;try{if(!Hb(f(),e))return!1}catch(g){return!1}}}c=b.child;if(b.subtreeFlags&16384&&null!==c)c.return=b,b=c;else{if(b===a)break;for(;null===b.sibling;){if(null===b.return||b.return===a)return!0;b=b.return}b.sibling.return=b.return;b=b.sibling}}return!0}
function xf(a,b){b&=~nf;b&=~mf;a.suspendedLanes|=b;a.pingedLanes&=~b;for(a=a.expirationTimes;0<b;){var c=31-Ra(b),d=1<<c;a[c]=-1;b&=~d}}
function zf(a){if(0!==(I&6))throw Error("Should not already be working.");Cf();var b=Xa(a,0);if(0===(b&1))return yf(a,q()),null;var c=Df(a,b);if(0!==a.tag&&2===c){var d=$a(a);0!==d&&(b=d,c=Kf(a,d))}if(1===c)throw c=lf,Ff(a,0),xf(a,b),yf(a,q()),c;if(6===c)throw Error("Root did not complete. This is a bug in React.");a.finishedWork=a.current.alternate;a.finishedLanes=b;Mf(a,Z,pf);yf(a,q());return null}
function Nf(a){null!==rf&&0===rf.tag&&0===(I&6)&&Cf();var b=I;I|=1;var c=kf.transition,d=v;try{if(kf.transition=null,v=1,a)return a()}finally{v=d,kf.transition=c,I=b,0===(I&6)&&Kb()}}function Ke(){R=je.current;x(je)}
function Ff(a,b){a.finishedWork=null;a.finishedLanes=0;var c=a.timeoutHandle;-1!==c&&(a.timeoutHandle=-1,pb(c));if(null!==X)for(c=X.return;null!==c;){var d=c;Rb(d);switch(d.tag){case 1:d=d.type.childContextTypes;null!==d&&void 0!==d&&Bb();break;case 3:Sc();x(C);x(z);Xc();break;case 5:Uc(d);break;case 4:Sc();break;case 13:x(K);break;case 19:x(K);break;case 10:bc(d.type._context);break;case 22:case 23:Ke()}c=c.return}P=a;X=a=Fc(a.current,null);Y=R=b;T=0;lf=null;nf=mf=J=0;Z=of=null;if(null!==ec){for(b=
0;b<ec.length;b++)if(c=ec[b],d=c.interleaved,null!==d){c.interleaved=null;var e=d.next,f=c.pending;if(null!==f){var g=f.next;f.next=e;d.next=g}c.pending=d}ec=null}return a}
function Hf(a,b){do{var c=X;try{ac();Yc.current=id;if(ad){for(var d=L.memoizedState;null!==d;){var e=d.queue;null!==e&&(e.pending=null);d=d.next}ad=!1}$c=0;N=M=L=null;bd=!1;jf.current=null;if(null===c||null===c.return){T=1;lf=b;X=null;break}a:{var f=a,g=c.return,h=c,k=b;b=Y;h.flags|=32768;if(null!==k&&"object"===typeof k&&"function"===typeof k.then){var l=k,m=h,w=m.tag;if(0===(m.mode&1)&&(0===w||11===w||15===w)){var p=m.alternate;p?(m.updateQueue=p.updateQueue,m.memoizedState=p.memoizedState,m.lanes=
p.lanes):(m.updateQueue=null,m.memoizedState=null)}b:{m=g;do{var B;if(B=13===m.tag){var r=m.memoizedState;B=null!==r?null!==r.dehydrated?!0:!1:!0}if(B){var G=m;break b}m=m.return}while(null!==m);G=null}if(null!==G){G.flags&=-257;k=G;m=b;if(0===(k.mode&1))if(k===g)k.flags|=65536;else{k.flags|=128;h.flags|=131072;h.flags&=-52805;if(1===h.tag)if(null===h.alternate)h.tag=17;else{var Ka=lc(-1,1);Ka.tag=2;mc(h,Ka,1)}h.lanes|=1}else k.flags|=65536,k.lanes=m;G.mode&1&&$d(f,l,b);b=G;f=l;var A=b.updateQueue;
if(null===A){var n=new Set;n.add(f);b.updateQueue=n}else A.add(f);break a}else{if(0===(b&1)){$d(f,l,b);we();break a}k=Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.")}}f=k=Rd(k,h);4!==T&&(T=2);null===of?of=[f]:of.push(f);f=g;do{switch(f.tag){case 3:l=k;f.flags|=65536;b&=-b;f.lanes|=b;var t=Vd(f,l,b);oc(f,t);break a;case 1:l=k;var hb=f.type,Ya=f.stateNode;
if(0===(f.flags&128)&&("function"===typeof hb.getDerivedStateFromError||null!==Ya&&"function"===typeof Ya.componentDidCatch&&(null===Zd||!Zd.has(Ya)))){f.flags|=65536;b&=-b;f.lanes|=b;var If=Yd(f,l,b);oc(f,If);break a}}f=f.return}while(null!==f)}Of(c)}catch(Jf){b=Jf;X===c&&null!==c&&(X=c=c.return);continue}break}while(1)}function Ef(){var a=hf.current;hf.current=id;return null===a?id:a}function we(){if(0===T||3===T||2===T)T=4;null===P||0===(J&268435455)&&0===(mf&268435455)||xf(P,Y)}
function Df(a,b){var c=I;I|=2;var d=Ef();if(P!==a||Y!==b)pf=null,Ff(a,b);do try{Pf();break}catch(e){Hf(a,e)}while(1);ac();I=c;hf.current=d;if(null!==X)throw Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");P=null;Y=0;return T}function Pf(){for(;null!==X;)Qf(X)}function Gf(){for(;null!==X&&!Ga();)Qf(X)}function Qf(a){var b=Rf(a.alternate,a,R);a.memoizedProps=a.pendingProps;null===b?Of(a):X=b;jf.current=null}
function Of(a){var b=a;do{var c=b.alternate;a=b.return;if(0===(b.flags&32768)){if(c=He(c,b,R),null!==c){X=c;return}}else{c=Le(c,b);if(null!==c){c.flags&=32767;X=c;return}if(null!==a)a.flags|=32768,a.subtreeFlags=0,a.deletions=null;else{T=6;X=null;return}}b=b.sibling;if(null!==b){X=b;return}X=b=a}while(null!==b);0===T&&(T=5)}function Mf(a,b,c){var d=v,e=kf.transition;try{kf.transition=null,v=1,Sf(a,b,c,d)}finally{kf.transition=e,v=d}return null}
function Sf(a,b,c,d){do Cf();while(null!==rf);if(0!==(I&6))throw Error("Should not already be working.");c=a.finishedWork;var e=a.finishedLanes;if(null===c)return null;a.finishedWork=null;a.finishedLanes=0;if(c===a.current)throw Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");a.callbackNode=null;a.callbackPriority=0;var f=c.lanes|c.childLanes;db(a,f);a===P&&(X=P=null,Y=0);0===(c.subtreeFlags&2064)&&0===(c.flags&2064)||qf||(qf=!0,
Af(La,function(){Cf();return null}));f=0!==(c.flags&15990);if(0!==(c.subtreeFlags&15990)||f){f=kf.transition;kf.transition=null;var g=v;v=1;var h=I;I|=4;jf.current=null;Qe(a,c);cf(c,a);a.current=c;ff(c,a,e);Ha();I=h;v=g;kf.transition=f}else a.current=c;qf&&(qf=!1,rf=a,sf=e);f=a.pendingLanes;0===f&&(Zd=null);Pa(c.stateNode,d);yf(a,q());if(null!==b)for(d=a.onRecoverableError,c=0;c<b.length;c++)e=b[c],d(e.value,{componentStack:e.stack,digest:e.digest});if(Wd)throw Wd=!1,a=Xd,Xd=null,a;0!==(sf&1)&&0!==
a.tag&&Cf();f=a.pendingLanes;0!==(f&1)?a===uf?tf++:(tf=0,uf=a):tf=0;Kb();return null}
function Cf(){if(null!==rf){var a=fb(sf),b=kf.transition,c=v;try{kf.transition=null;v=16>a?16:a;if(null===rf)var d=!1;else{a=rf;rf=null;sf=0;if(0!==(I&6))throw Error("Cannot flush passive effects while already rendering.");var e=I;I|=4;for(U=a.current;null!==U;){var f=U,g=f.child;if(0!==(U.flags&16)){var h=f.deletions;if(null!==h){for(var k=0;k<h.length;k++){var l=h[k];for(U=l;null!==U;){var m=U;switch(m.tag){case 0:case 11:case 15:Re(8,m,f)}var w=m.child;if(null!==w)w.return=m,U=w;else for(;null!==
U;){m=U;var p=m.sibling,B=m.return;Te(m);if(m===l){U=null;break}if(null!==p){p.return=B;U=p;break}U=B}}}var r=f.alternate;if(null!==r){var G=r.child;if(null!==G){r.child=null;do{var Ka=G.sibling;G.sibling=null;G=Ka}while(null!==G)}}U=f}}if(0!==(f.subtreeFlags&2064)&&null!==g)g.return=f,U=g;else b:for(;null!==U;){f=U;if(0!==(f.flags&2048))switch(f.tag){case 0:case 11:case 15:Re(9,f,f.return)}var A=f.sibling;if(null!==A){A.return=f.return;U=A;break b}U=f.return}}var n=a.current;for(U=n;null!==U;){g=
U;var t=g.child;if(0!==(g.subtreeFlags&2064)&&null!==t)t.return=g,U=t;else b:for(g=n;null!==U;){h=U;if(0!==(h.flags&2048))try{switch(h.tag){case 0:case 11:case 15:Se(9,h)}}catch(Ya){V(h,h.return,Ya)}if(h===g){U=null;break b}var hb=h.sibling;if(null!==hb){hb.return=h.return;U=hb;break b}U=h.return}}I=e;Kb();if(Oa&&"function"===typeof Oa.onPostCommitFiberRoot)try{Oa.onPostCommitFiberRoot(Na,a)}catch(Ya){}d=!0}return d}finally{v=c,kf.transition=b}}return!1}
function Tf(a,b,c){b=Rd(c,b);b=Vd(a,b,1);a=mc(a,b,1);b=tc();null!==a&&(cb(a,1,b),yf(a,b))}function V(a,b,c){if(3===a.tag)Tf(a,a,c);else for(b=a.return;null!==b;){if(3===b.tag){Tf(b,a,c);break}else if(1===b.tag){var d=b.stateNode;if("function"===typeof b.type.getDerivedStateFromError||"function"===typeof d.componentDidCatch&&(null===Zd||!Zd.has(d))){a=Rd(c,a);a=Yd(b,a,1);b=mc(b,a,1);a=tc();null!==b&&(cb(b,1,a),yf(b,a));break}}b=b.return}}
function ae(a,b,c){var d=a.pingCache;null!==d&&d.delete(b);b=tc();a.pingedLanes|=a.suspendedLanes&c;P===a&&(Y&c)===c&&(4===T||3===T&&(Y&130023424)===Y&&500>q()-ef?Ff(a,0):nf|=c);yf(a,b)}function Uf(a,b){0===b&&(0===(a.mode&1)?b=1:(b=Va,Va<<=1,0===(Va&130023424)&&(Va=4194304)));var c=tc();a=hc(a,b);null!==a&&(cb(a,b,c),yf(a,c))}function xe(a){var b=a.memoizedState,c=0;null!==b&&(c=b.retryLane);Uf(a,c)}
function af(a,b){var c=0;switch(a.tag){case 13:var d=a.stateNode;var e=a.memoizedState;null!==e&&(c=e.retryLane);break;case 19:d=a.stateNode;break;default:throw Error("Pinged unknown suspense boundary type. This is probably a bug in React.");}null!==d&&d.delete(b);Uf(a,c)}var Rf;
Rf=function(a,b,c){if(null!==a)if(a.memoizedProps!==b.pendingProps||C.current)F=!0;else{if(0===(a.lanes&c)&&0===(b.flags&128))return F=!1,Be(a,b,c);F=0!==(a.flags&131072)?!0:!1}else F=!1;b.lanes=0;switch(b.tag){case 2:var d=b.type;me(a,b);a=b.pendingProps;var e=Ab(b,z.current);dc(b,c);e=ed(null,b,d,a,e,c);b.flags|=1;if("object"===typeof e&&null!==e&&"function"===typeof e.render&&void 0===e.$$typeof){b.tag=1;b.memoizedState=null;b.updateQueue=null;if(D(d)){var f=!0;Eb(b)}else f=!1;b.memoizedState=
null!==e.state&&void 0!==e.state?e.state:null;jc(b);e.updater=wc;b.stateNode=e;e._reactInternals=b;Ac(b,d,a,c);b=ne(null,b,d,!0,f,c)}else b.tag=0,Q(null,b,e,c),b=b.child;return b;case 16:d=b.elementType;a:{me(a,b);a=b.pendingProps;e=d._init;d=e(d._payload);b.type=d;e=b.tag=Vf(d);a=Wb(d,a);switch(e){case 0:b=he(null,b,d,a,c);break a;case 1:b=le(null,b,d,a,c);break a;case 11:b=ce(null,b,d,a,c);break a;case 14:b=ee(null,b,d,Wb(d.type,a),c);break a}throw Error("Element type is invalid. Received a promise that resolves to: "+
d+". Lazy element type must resolve to a class or function.");}return b;case 0:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:Wb(d,e),he(a,b,d,e,c);case 1:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:Wb(d,e),le(a,b,d,e,c);case 3:oe(b);if(null===a)throw Error("Should have a current fiber. This is a bug in React.");e=b.pendingProps;d=b.memoizedState.element;kc(a,b);pc(b,e,null,c);e=b.memoizedState.element;e===d?b=de(a,b,c):(Q(a,b,e,c),b=b.child);return b;case 5:return Tc(b),d=b.pendingProps.children,
ke(a,b),Q(a,b,d,c),b.child;case 6:return null;case 13:return re(a,b,c);case 4:return Rc(b,b.stateNode.containerInfo),d=b.pendingProps,null===a?b.child=Kc(b,null,d,c):Q(a,b,d,c),b.child;case 11:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:Wb(d,e),ce(a,b,d,e,c);case 7:return Q(a,b,b.pendingProps,c),b.child;case 8:return Q(a,b,b.pendingProps.children,c),b.child;case 12:return Q(a,b,b.pendingProps.children,c),b.child;case 10:a:{d=b.type._context;e=b.pendingProps;f=b.memoizedProps;var g=e.value;
y(Xb,d._currentValue2);d._currentValue2=g;if(null!==f)if(Hb(f.value,g)){if(f.children===e.children&&!C.current){b=de(a,b,c);break a}}else for(f=b.child,null!==f&&(f.return=b);null!==f;){var h=f.dependencies;if(null!==h){g=f.child;for(var k=h.firstContext;null!==k;){if(k.context===d){if(1===f.tag){k=lc(-1,c&-c);k.tag=2;var l=f.updateQueue;if(null!==l){l=l.shared;var m=l.pending;null===m?k.next=k:(k.next=m.next,m.next=k);l.pending=k}}f.lanes|=c;k=f.alternate;null!==k&&(k.lanes|=c);cc(f.return,c,b);
h.lanes|=c;break}k=k.next}}else if(10===f.tag)g=f.type===b.type?null:f.child;else if(18===f.tag){g=f.return;if(null===g)throw Error("We just came from a parent so we must have had a parent. This is a bug in React.");g.lanes|=c;h=g.alternate;null!==h&&(h.lanes|=c);cc(g,c,b);g=f.sibling}else g=f.child;if(null!==g)g.return=f;else for(g=f;null!==g;){if(g===b){g=null;break}f=g.sibling;if(null!==f){f.return=g.return;g=f;break}g=g.return}f=g}Q(a,b,e.children,c);b=b.child}return b;case 9:return e=b.type,
d=b.pendingProps.children,dc(b,c),e=H(e),d=d(e),b.flags|=1,Q(a,b,d,c),b.child;case 14:return d=b.type,e=Wb(d,b.pendingProps),e=Wb(d.type,e),ee(a,b,d,e,c);case 15:return ge(a,b,b.type,b.pendingProps,c);case 17:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:Wb(d,e),me(a,b),b.tag=1,D(d)?(a=!0,Eb(b)):a=!1,dc(b,c),yc(b,d,e),Ac(b,d,e,c),ne(null,b,d,!0,a,c);case 19:return Ae(a,b,c);case 22:return ie(a,b,c)}throw Error("Unknown unit of work tag ("+b.tag+"). This error is likely caused by a bug in React. Please file an issue.");
};function Af(a,b){return Ea(a,b)}function Wf(a,b,c,d){this.tag=a;this.key=c;this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null;this.index=0;this.ref=null;this.pendingProps=b;this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null;this.mode=d;this.subtreeFlags=this.flags=0;this.deletions=null;this.childLanes=this.lanes=0;this.alternate=null}function Xf(a,b,c,d){return new Wf(a,b,c,d)}
function fe(a){a=a.prototype;return!(!a||!a.isReactComponent)}function Vf(a){if("function"===typeof a)return fe(a)?1:0;if(void 0!==a&&null!==a){a=a.$$typeof;if(a===pa)return 11;if(a===sa)return 14}return 2}
function Fc(a,b){var c=a.alternate;null===c?(c=Xf(a.tag,b,a.key,a.mode),c.elementType=a.elementType,c.type=a.type,c.stateNode=a.stateNode,c.alternate=a,a.alternate=c):(c.pendingProps=b,c.type=a.type,c.flags=0,c.subtreeFlags=0,c.deletions=null);c.flags=a.flags&14680064;c.childLanes=a.childLanes;c.lanes=a.lanes;c.child=a.child;c.memoizedProps=a.memoizedProps;c.memoizedState=a.memoizedState;c.updateQueue=a.updateQueue;b=a.dependencies;c.dependencies=null===b?null:{lanes:b.lanes,firstContext:b.firstContext};
c.sibling=a.sibling;c.index=a.index;c.ref=a.ref;return c}
function Hc(a,b,c,d,e,f){var g=2;d=a;if("function"===typeof a)fe(a)&&(g=1);else if("string"===typeof a)g=5;else a:switch(a){case ka:return Jc(c.children,e,f,b);case la:g=8;e|=8;break;case ma:return a=Xf(12,c,b,e|2),a.elementType=ma,a.lanes=f,a;case qa:return a=Xf(13,c,b,e),a.elementType=qa,a.lanes=f,a;case ra:return a=Xf(19,c,b,e),a.elementType=ra,a.lanes=f,a;case ua:return se(c,e,f,b);default:if("object"===typeof a&&null!==a)switch(a.$$typeof){case na:g=10;break a;case oa:g=9;break a;case pa:g=11;
break a;case sa:g=14;break a;case ta:g=16;d=null;break a}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==a?a:typeof a)+"."));}b=Xf(g,c,b,e);b.elementType=a;b.type=d;b.lanes=f;return b}function Jc(a,b,c,d){a=Xf(7,a,d,b);a.lanes=c;return a}function se(a,b,c,d){a=Xf(22,a,d,b);a.elementType=ua;a.lanes=c;a.stateNode={isHidden:!1};return a}function Gc(a,b,c){a=Xf(6,a,null,b);a.lanes=c;return a}
function Ic(a,b,c){b=Xf(4,null!==a.children?a.children:[],a.key,b);b.lanes=c;b.stateNode={containerInfo:a.containerInfo,pendingChildren:null,implementation:a.implementation};return b}
function Yf(a,b,c,d,e){this.tag=b;this.containerInfo=a;this.finishedWork=this.pingCache=this.current=this.pendingChildren=null;this.timeoutHandle=-1;this.callbackNode=this.pendingContext=this.context=null;this.callbackPriority=0;this.eventTimes=bb(0);this.expirationTimes=bb(-1);this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0;this.entanglements=bb(0);this.identifierPrefix=d;this.onRecoverableError=e}
function Zf(a,b,c,d,e,f,g){a=new Yf(a,b,!1,f,g);1===b?(b=1,!0===d&&(b|=8)):b=0;d=Xf(3,null,null,b);a.current=d;d.stateNode=a;d.memoizedState={element:null,isDehydrated:!1,cache:null,transitions:null,pendingSuspenseBoundaries:null};jc(d);return a}
function $f(a,b,c,d){var e=b.current,f=tc(),g=uc(e);a:if(c){c=c._reactInternals;b:{if(za(c)!==c||1!==c.tag)throw Error("Expected subtree parent to be a mounted class component. This error is likely caused by a bug in React. Please file an issue.");var h=c;do{switch(h.tag){case 3:h=h.stateNode.context;break b;case 1:if(D(h.type)){h=h.stateNode.__reactInternalMemoizedMergedChildContext;break b}}h=h.return}while(null!==h);throw Error("Found unexpected detached subtree parent. This error is likely caused by a bug in React. Please file an issue.");
}if(1===c.tag){var k=c.type;if(D(k)){c=Db(c,k,h);break a}}c=h}else c=yb;null===b.context?b.context=c:b.pendingContext=c;b=lc(f,g);b.payload={element:a};d=void 0===d?null:d;null!==d&&(b.callback=d);a=mc(e,b,g);null!==a&&(vc(a,e,g,f),nc(a,e,g));return g}function ag(){return null}var bg=aa.unstable_act,cg={createNodeMock:function(){return null}};
function dg(a){if(a.isHidden)return null;switch(a.tag){case "TEXT":return a.text;case "INSTANCE":var b=a.props;var c=["children"];if(null==b)b={};else{var d={},e=Object.keys(b),f;for(f=0;f<e.length;f++){var g=e[f];0<=c.indexOf(g)||(d[g]=b[g])}b=d}c=null;if(a.children&&a.children.length)for(d=0;d<a.children.length;d++)e=dg(a.children[d]),null!==e&&(null===c?c=[e]:c.push(e));a={type:a.type,props:b,children:c};Object.defineProperty(a,"$$typeof",{value:Symbol.for("react.test.json")});return a;default:throw Error("Unexpected node type in toJSON: "+
a.tag);}}function eg(a){if(!a)return null;a=fg(a);return 0===a.length?null:1===a.length?gg(a[0]):hg(a.map(gg))}function fg(a){for(var b=[];null!=a;)b.push(a),a=a.sibling;return b}function hg(a){var b=[];for(a=[{i:0,array:a}];a.length;)for(var c=a.pop();c.i<c.array.length;){var d=c.array[c.i];c.i+=1;if(Da(d)){a.push(c);a.push({i:0,array:d});break}b.push(d)}return b}
function gg(a){if(null==a)return null;switch(a.tag){case 3:return eg(a.child);case 4:return eg(a.child);case 1:return{nodeType:"component",type:a.type,props:fa({},a.memoizedProps),instance:a.stateNode,rendered:eg(a.child)};case 0:case 15:return{nodeType:"component",type:a.type,props:fa({},a.memoizedProps),instance:null,rendered:eg(a.child)};case 5:return{nodeType:"host",type:a.type,props:fa({},a.memoizedProps),instance:null,rendered:hg(fg(a.child).map(gg))};case 6:return a.stateNode.text;case 7:case 10:case 9:case 8:case 12:case 11:case 14:case 17:case 21:return eg(a.child);
default:throw Error("toTree() does not yet know how to handle nodes with tag="+a.tag);}}var ig=new Set([0,1,5,11,14,15,3]);function jg(a){var b=[],c=a;if(null===c.child)return b;c.child.return=c;c=c.child;a:for(;;){var d=!1;ig.has(c.tag)?b.push(kg(c)):6===c.tag?b.push(""+c.memoizedProps):d=!0;if(d&&null!==c.child)c.child.return=c,c=c.child;else{for(;null===c.sibling;){if(c.return===a)break a;c=c.return}c.sibling.return=c.return;c=c.sibling}}return b}
var ng=function(){function a(a){if(!ig.has(a.tag))throw Error("Unexpected object passed to ReactTestInstance constructor (tag: "+a.tag+"). This is probably a bug in React.");this._fiber=a}var b=a.prototype;b._currentFiber=function(){var a=Ba(this._fiber);if(null===a)throw Error("Can't read from currently-mounting component. This error is likely caused by a bug in React. Please file an issue.");return a};b.find=function(a){return lg(this.findAll(a,{deep:!1}),"matching custom predicate: "+a.toString())};
b.findByType=function(a){return lg(this.findAllByType(a,{deep:!1}),'with node type: "'+(xa(a)||"Unknown")+'"')};b.findByProps=function(a){return lg(this.findAllByProps(a,{deep:!1}),"with props: "+JSON.stringify(a))};b.findAll=function(a){return mg(this,a,1<arguments.length&&void 0!==arguments[1]?arguments[1]:null)};b.findAllByType=function(a){return mg(this,function(b){return b.type===a},1<arguments.length&&void 0!==arguments[1]?arguments[1]:null)};b.findAllByProps=function(a){return mg(this,function(b){var c;
if(c=b.props)a:{for(var d in a)if(b.props[d]!==a[d]){c=!1;break a}c=!0}return c},1<arguments.length&&void 0!==arguments[1]?arguments[1]:null)};ea(a,[{key:"instance",get:function(){return 5===this._fiber.tag?lb(this._fiber.stateNode):this._fiber.stateNode}},{key:"type",get:function(){return this._fiber.type}},{key:"props",get:function(){return this._currentFiber().memoizedProps}},{key:"parent",get:function(){for(var a=this._fiber.return;null!==a;){if(ig.has(a.tag)){if(3===a.tag&&2>jg(a).length)break;
return kg(a)}a=a.return}return null}},{key:"children",get:function(){return jg(this._currentFiber())}}]);return a}();function mg(a,b,c){var d=c?c.deep:!0,e=[];if(b(a)&&(e.push(a),!d))return e;a.children.forEach(function(a){"string"!==typeof a&&e.push.apply(e,mg(a,b,c))});return e}function lg(a,b){if(1===a.length)return a[0];throw Error((0===a.length?"No instances found ":"Expected 1 but found "+a.length+" instances ")+b);}function og(a){console.error(a)}var pg=new WeakMap;
function kg(a){var b=pg.get(a);void 0===b&&null!==a.alternate&&(b=pg.get(a.alternate));void 0===b&&(b=new ng(a),pg.set(a,b));return b}var qg={findFiberByHostInstance:function(){throw Error("TestRenderer does not support findFiberByHostInstance()");},bundleType:0,version:"18.2.0",rendererPackageName:"react-test-renderer"};
var rg={bundleType:qg.bundleType,version:qg.version,rendererPackageName:qg.rendererPackageName,rendererConfig:qg.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ha.ReactCurrentDispatcher,findHostInstanceByFiber:function(a){a=Ba(a);a=null!==a?Ca(a):null;return null===a?null:a.stateNode},
findFiberByHostInstance:qg.findFiberByHostInstance||ag,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var sg=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!sg.isDisabled&&sg.supportsFiber)try{Na=sg.inject(rg),Oa=sg}catch(a){}}exports._Scheduler=ba;exports.act=bg;
exports.create=function(a,b){var c=cg.createNodeMock,d=!1,e=!1;"object"===typeof b&&null!==b&&("function"===typeof b.createNodeMock&&(c=b.createNodeMock),!0===b.unstable_isConcurrent&&(d=!0),!0===b.unstable_strictMode&&(e=!0));var f={children:[],createNodeMock:c,tag:"CONTAINER"},g=Zf(f,d?1:0,null,e,null,"",og);if(null==g)throw Error("something went wrong");$f(a,g,null,null);a={_Scheduler:ba,root:void 0,toJSON:function(){if(null==g||null==g.current||null==f||0===f.children.length)return null;if(1===
f.children.length)return dg(f.children[0]);if(2===f.children.length&&!0===f.children[0].isHidden&&!1===f.children[1].isHidden)return dg(f.children[1]);var a=null;if(f.children&&f.children.length)for(var b=0;b<f.children.length;b++){var c=dg(f.children[b]);null!==c&&(null===a?a=[c]:a.push(c))}return a},toTree:function(){return null==g||null==g.current?null:gg(g.current)},update:function(a){null!=g&&null!=g.current&&$f(a,g,null,null)},unmount:function(){null!=g&&null!=g.current&&($f(null,g,null,null),
g=f=null)},getInstance:function(){if(null==g||null==g.current)return null;a:{var a=g.current;if(a.child)switch(a.child.tag){case 5:a=lb(a.child.stateNode);break a;default:a=a.child.stateNode}else a=null}return a},unstable_flushSync:Nf};Object.defineProperty(a,"root",{configurable:!0,enumerable:!0,get:function(){if(null===g)throw Error("Can't access .root on unmounted test renderer");var a=jg(g.current);if(0===a.length)throw Error("Can't access .root on unmounted test renderer");return 1===a.length?
a[0]:kg(g.current)}});return a};exports.unstable_batchedUpdates=function(a,b){var c=I;I|=1;try{return a(b)}finally{I=c,0===I&&(Je=q()+500,Ib&&Kb())}};
