/**
 * @license React
 * react-test-renderer.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(Y,Aa){"object"===typeof exports&&"undefined"!==typeof module?Aa(exports,require("scheduler/unstable_mock"),require("scheduler")):"function"===typeof define&&define.amd?define(["exports","react","scheduler/unstable_mock","scheduler"],Aa):(Y=Y||self,Aa(Y.ReactTestRenderer={},<PERSON><PERSON>,Y.Scheduler<PERSON>,Y.Scheduler))})(this,function(Y,Aa,vd,la){function wd(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1;d.configurable=!0;"value"in d&&(d.writable=
!0);Object.defineProperty(a,d.key,d)}}function ef(a,b,c){b&&wd(a.prototype,b);c&&wd(a,c);return a}function Za(a){if(null===a||"object"!==typeof a)return null;a=xd&&a[xd]||a["@@iterator"];return"function"===typeof a?a:null}function pb(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Na:return"Fragment";case $a:return"Portal";case gc:return"Profiler";case hc:return"StrictMode";case ic:return"Suspense";case jc:return"SuspenseList"}if("object"===
typeof a)switch(a.$$typeof){case yd:return(a.displayName||"Context")+".Consumer";case zd:return(a._context.displayName||"Context")+".Provider";case kc:var b=a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case lc:return b=a.displayName||null,null!==b?b:pb(a.type)||"Memo";case pa:b=a._payload;a=a._init;try{return pb(a(b))}catch(c){}}return null}function ff(a){var b=a.type;switch(a.tag){case 24:return"Cache";case 9:return(b.displayName||"Context")+
".Consumer";case 10:return(b._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return a=b.render,a=a.displayName||a.name||"",b.displayName||(""!==a?"ForwardRef("+a+")":"ForwardRef");case 7:return"Fragment";case 5:return b;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return pb(b);case 8:return b===hc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";
case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof b)return b.displayName||b.name||null;if("string"===typeof b)return b}return null}function qb(a){var b=a,c=a;if(a.alternate)for(;b.return;)b=b.return;else{a=b;do b=a,0!==(b.flags&4098)&&(c=b.return),a=b.return;while(a)}return 3===b.tag?c:null}function Ad(a){if(qb(a)!==a)throw Error("Unable to find node on an unmounted component.");}function Bd(a){var b=a.alternate;if(!b){b=qb(a);if(null===b)throw Error("Unable to find node on an unmounted component.");
return b!==a?null:a}for(var c=a,d=b;;){var e=c.return;if(null===e)break;var f=e.alternate;if(null===f){d=e.return;if(null!==d){c=d;continue}break}if(e.child===f.child){for(f=e.child;f;){if(f===c)return Ad(e),a;if(f===d)return Ad(e),b;f=f.sibling}throw Error("Unable to find node on an unmounted component.");}if(c.return!==d.return)c=e,d=f;else{for(var g=!1,h=e.child;h;){if(h===c){g=!0;c=e;d=f;break}if(h===d){g=!0;d=e;c=f;break}h=h.sibling}if(!g){for(h=f.child;h;){if(h===c){g=!0;c=f;d=e;break}if(h===
d){g=!0;d=f;c=e;break}h=h.sibling}if(!g)throw Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.");}}if(c.alternate!==d)throw Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.");}if(3!==c.tag)throw Error("Unable to find node on an unmounted component.");return c.stateNode.current===c?a:b}function Cd(a){if(5===a.tag||6===a.tag)return a;for(a=
a.child;null!==a;){var b=Cd(a);if(null!==b)return b;a=a.sibling}return null}function gf(a,b){if(da&&"function"===typeof da.onCommitFiberRoot)try{da.onCommitFiberRoot(rb,a,void 0,128===(a.current.flags&128))}catch(c){}}function hf(a){a>>>=0;return 0===a?32:31-(jf(a)/kf|0)|0}function sb(a){switch(a&-a){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return a&
4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return a&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return a}}function tb(a,b){var c=a.pendingLanes;if(0===c)return 0;var d=0,e=a.suspendedLanes,f=a.pingedLanes,g=c&268435455;if(0!==g){var h=g&~e;0!==h?d=sb(h):(f&=g,0!==f&&(d=sb(f)))}else g=c&~e,0!==g?d=sb(g):0!==f&&(d=sb(f));if(0===d)return 0;if(0!==b&&b!==d&&0===(b&e)&&
(e=d&-d,f=b&-b,e>=f||16===e&&0!==(f&4194240)))return b;0!==(d&4)&&(d|=c&16);b=a.entangledLanes;if(0!==b)for(a=a.entanglements,b&=d;0<b;)c=31-Ba(b),e=1<<c,d|=a[c],b&=~e;return d}function lf(a,b){switch(a){case 1:case 2:case 4:return b+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return b+5E3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;
case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function mc(a){a=a.pendingLanes&-1073741825;return 0!==a?a:a&1073741824?1073741824:0}function Dd(){var a=ub;ub<<=1;0===(ub&4194240)&&(ub=64);return a}function nc(a){for(var b=[],c=0;31>c;c++)b.push(a);return b}function vb(a,b,c){a.pendingLanes|=b;536870912!==b&&(a.suspendedLanes=0,a.pingedLanes=0);a=a.eventTimes;b=31-Ba(b);a[b]=c}function mf(a,b){var c=a.pendingLanes&~b;a.pendingLanes=b;a.suspendedLanes=0;a.pingedLanes=
0;a.expiredLanes&=b;a.mutableReadLanes&=b;a.entangledLanes&=b;b=a.entanglements;var d=a.eventTimes;for(a=a.expirationTimes;0<c;){var e=31-Ba(c),f=1<<e;b[e]=0;d[e]=-1;a[e]=-1;c&=~f}}function Ed(a,b){var c=a.entangledLanes|=b;for(a=a.entanglements;c;){var d=31-Ba(c),e=1<<d;e&b|a[d]&b&&(a[d]|=b);c&=~e}}function Fd(a){a&=-a;return 1<a?4<a?0!==(a&268435455)?16:536870912:4:1}function Oa(){throw Error("The current renderer does not support hydration. This error is likely caused by a bug in React. Please file an issue.");
}function wb(a){switch(a.tag){case "INSTANCE":var b=a.rootContainerInstance.createNodeMock;b=b({type:a.type,props:a.props});"object"===typeof b&&null!==b&&nf.set(b,a);return b;default:return a}}function Gd(a,b){var c=a.children.indexOf(b);-1!==c&&a.children.splice(c,1);a.children.push(b)}function Hd(a,b,c){var d=a.children.indexOf(b);-1!==d&&a.children.splice(d,1);c=a.children.indexOf(c);a.children.splice(c,0,b)}function Id(a,b){b=a.children.indexOf(b);a.children.splice(b,1)}function ab(a,b,c){if(void 0===
oc)try{throw Error();}catch(d){oc=(b=d.stack.trim().match(/\n( *(at )?)/))&&b[1]||""}return"\n"+oc+a}function pc(a,b){if(!a||qc)return"";qc=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(b)if(b=function(){throw Error();},Object.defineProperty(b.prototype,"props",{set:function(){throw Error();}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(b,[])}catch(l){var d=l}Reflect.construct(a,[],b)}else{try{b.call()}catch(l){d=l}a.call(b.prototype)}else{try{throw Error();
}catch(l){d=l}a()}}catch(l){if(l&&d&&"string"===typeof l.stack){for(var e=l.stack.split("\n"),f=d.stack.split("\n"),g=e.length-1,h=f.length-1;1<=g&&0<=h&&e[g]!==f[h];)h--;for(;1<=g&&0<=h;g--,h--)if(e[g]!==f[h]){if(1!==g||1!==h){do if(g--,h--,0>h||e[g]!==f[h]){var k="\n"+e[g].replace(" at new "," at ");a.displayName&&k.includes("<anonymous>")&&(k=k.replace("<anonymous>",a.displayName));return k}while(1<=g&&0<=h)}break}}}finally{qc=!1,Error.prepareStackTrace=c}return(a=a?a.displayName||a.name:"")?ab(a):
""}function qa(a){return{current:a}}function q(a,b){0>Pa||(a.current=rc[Pa],rc[Pa]=null,Pa--)}function u(a,b,c){Pa++;rc[Pa]=a.current;a.current=b}function Qa(a,b){var c=a.type.contextTypes;if(!c)return ra;var d=a.stateNode;if(d&&d.__reactInternalMemoizedUnmaskedChildContext===b)return d.__reactInternalMemoizedMaskedChildContext;var e={},f;for(f in c)e[f]=b[f];d&&(a=a.stateNode,a.__reactInternalMemoizedUnmaskedChildContext=b,a.__reactInternalMemoizedMaskedChildContext=e);return e}function N(a){a=a.childContextTypes;
return null!==a&&void 0!==a}function Jd(a,b,c){if(x.current!==ra)throw Error("Unexpected context found on stack. This error is likely caused by a bug in React. Please file an issue.");u(x,b);u(E,c)}function Kd(a,b,c){var d=a.stateNode;b=b.childContextTypes;if("function"!==typeof d.getChildContext)return c;d=d.getChildContext();for(var e in d)if(!(e in b))throw Error((ff(a)||"Unknown")+'.getChildContext(): key "'+e+'" is not defined in childContextTypes.');return Ca({},c,d)}function xb(a){a=(a=a.stateNode)&&
a.__reactInternalMemoizedMergedChildContext||ra;Da=x.current;u(x,a);u(E,E.current);return!0}function Ld(a,b,c){var d=a.stateNode;if(!d)throw Error("Expected to have an instance by this point. This error is likely caused by a bug in React. Please file an issue.");c?(a=Kd(a,b,Da),d.__reactInternalMemoizedMergedChildContext=a,q(E),q(x),u(x,a)):q(E);u(E,c)}function of(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}function Ea(){if(!sc&&null!==S){sc=!0;var a=0,b=B;try{var c=S;for(B=1;a<c.length;a++){var d=
c[a];do d=d(!0);while(null!==d)}S=null;yb=!1}catch(e){throw null!==S&&(S=S.slice(a+1)),tc(uc,Ea),e;}finally{B=b,sc=!1}}return null}function vc(a){for(;a===Md;)Md=wc[--zb],wc[zb]=null,--zb,wc[zb]=null;for(;a===Nd;)Nd=Ab[--Ra],Ab[Ra]=null,--Ra,Ab[Ra]=null,--Ra,Ab[Ra]=null}function Bb(a,b){if(Z(a,b))return!0;if("object"!==typeof a||null===a||"object"!==typeof b||null===b)return!1;var c=Object.keys(a),d=Object.keys(b);if(c.length!==d.length)return!1;for(d=0;d<c.length;d++){var e=c[d];if(!pf.call(b,e)||
!Z(a[e],b[e]))return!1}return!0}function qf(a){switch(a.tag){case 5:return ab(a.type);case 16:return ab("Lazy");case 13:return ab("Suspense");case 19:return ab("SuspenseList");case 0:case 2:case 15:return a=pc(a.type,!1),a;case 11:return a=pc(a.type.render,!1),a;case 1:return a=pc(a.type,!0),a;default:return""}}function aa(a,b){if(a&&a.defaultProps){b=Ca({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}function xc(){yc=Sa=Cb=null}function zc(a,b){b=Db.current;q(Db);
a._currentValue2=b}function Ac(a,b,c){for(;null!==a;){var d=a.alternate;(a.childLanes&b)!==b?(a.childLanes|=b,null!==d&&(d.childLanes|=b)):null!==d&&(d.childLanes&b)!==b&&(d.childLanes|=b);if(a===c)break;a=a.return}}function Ta(a,b){Cb=a;yc=Sa=null;a=a.dependencies;null!==a&&null!==a.firstContext&&(0!==(a.lanes&b)&&(O=!0),a.firstContext=null)}function T(a){var b=a._currentValue2;if(yc!==a)if(a={context:a,memoizedValue:b,next:null},null===Sa){if(null===Cb)throw Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");
Sa=a;Cb.dependencies={lanes:0,firstContext:a}}else Sa=Sa.next=a;return b}function Bc(a){null===Fa?Fa=[a]:Fa.push(a)}function Od(a,b,c,d){var e=b.interleaved;null===e?(c.next=c,Bc(b)):(c.next=e.next,e.next=c);b.interleaved=c;return Ua(a,d)}function Ua(a,b){a.lanes|=b;var c=a.alternate;null!==c&&(c.lanes|=b);c=a;for(a=a.return;null!==a;)a.childLanes|=b,c=a.alternate,null!==c&&(c.childLanes|=b),c=a,a=a.return;return 3===c.tag?c.stateNode:null}function Cc(a){a.updateQueue={baseState:a.memoizedState,firstBaseUpdate:null,
lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Pd(a,b){a=a.updateQueue;b.updateQueue===a&&(b.updateQueue={baseState:a.baseState,firstBaseUpdate:a.firstBaseUpdate,lastBaseUpdate:a.lastBaseUpdate,shared:a.shared,effects:a.effects})}function sa(a,b){return{eventTime:a,lane:b,tag:0,payload:null,callback:null,next:null}}function Ga(a,b,c){var d=a.updateQueue;if(null===d)return null;d=d.shared;if(0!==(m&2)){var e=d.pending;null===e?b.next=b:(b.next=e.next,e.next=
b);d.pending=b;return rf(a,c)}e=d.interleaved;null===e?(b.next=b,Bc(d)):(b.next=e.next,e.next=b);d.interleaved=b;return Ua(a,c)}function Eb(a,b,c){b=b.updateQueue;if(null!==b&&(b=b.shared,0!==(c&4194240))){var d=b.lanes;d&=a.pendingLanes;c|=d;b.lanes=c;Ed(a,c)}}function Qd(a,b){var c=a.updateQueue,d=a.alternate;if(null!==d&&(d=d.updateQueue,c===d)){var e=null,f=null;c=c.firstBaseUpdate;if(null!==c){do{var g={eventTime:c.eventTime,lane:c.lane,tag:c.tag,payload:c.payload,callback:c.callback,next:null};
null===f?e=f=g:f=f.next=g;c=c.next}while(null!==c);null===f?e=f=b:f=f.next=b}else e=f=b;c={baseState:d.baseState,firstBaseUpdate:e,lastBaseUpdate:f,shared:d.shared,effects:d.effects};a.updateQueue=c;return}a=c.lastBaseUpdate;null===a?c.firstBaseUpdate=b:a.next=b;c.lastBaseUpdate=b}function Fb(a,b,c,d){var e=a.updateQueue;ta=!1;var f=e.firstBaseUpdate,g=e.lastBaseUpdate,h=e.shared.pending;if(null!==h){e.shared.pending=null;var k=h,l=k.next;k.next=null;null===g?f=l:g.next=l;g=k;var p=a.alternate;null!==
p&&(p=p.updateQueue,h=p.lastBaseUpdate,h!==g&&(null===h?p.firstBaseUpdate=l:h.next=l,p.lastBaseUpdate=k))}if(null!==f){var n=e.baseState;g=0;p=l=k=null;h=f;do{var C=h.lane,q=h.eventTime;if((d&C)===C){null!==p&&(p=p.next={eventTime:q,lane:0,tag:h.tag,payload:h.payload,callback:h.callback,next:null});a:{var m=a,r=h;C=b;q=c;switch(r.tag){case 1:m=r.payload;if("function"===typeof m){n=m.call(q,n,C);break a}n=m;break a;case 3:m.flags=m.flags&-65537|128;case 0:m=r.payload;C="function"===typeof m?m.call(q,
n,C):m;if(null===C||void 0===C)break a;n=Ca({},n,C);break a;case 2:ta=!0}}null!==h.callback&&0!==h.lane&&(a.flags|=64,C=e.effects,null===C?e.effects=[h]:C.push(h))}else q={eventTime:q,lane:C,tag:h.tag,payload:h.payload,callback:h.callback,next:null},null===p?(l=p=q,k=n):p=p.next=q,g|=C;h=h.next;if(null===h)if(h=e.shared.pending,null===h)break;else C=h,h=C.next,C.next=null,e.lastBaseUpdate=C,e.shared.pending=null}while(1);null===p&&(k=n);e.baseState=k;e.firstBaseUpdate=l;e.lastBaseUpdate=p;b=e.shared.interleaved;
if(null!==b){e=b;do g|=e.lane,e=e.next;while(e!==b)}else null===f&&(e.shared.lanes=0);U|=g;a.lanes=g;a.memoizedState=n}}function Rd(a,b,c){a=b.effects;b.effects=null;if(null!==a)for(b=0;b<a.length;b++){var d=a[b],e=d.callback;if(null!==e){d.callback=null;if("function"!==typeof e)throw Error("Invalid argument passed as callback. Expected a function. Instead received: "+e);e.call(c)}}}function Dc(a,b,c,d){b=a.memoizedState;c=c(d,b);c=null===c||void 0===c?b:Ca({},b,c);a.memoizedState=c;0===a.lanes&&
(a.updateQueue.baseState=c)}function Sd(a,b,c,d,e,f,g){a=a.stateNode;return"function"===typeof a.shouldComponentUpdate?a.shouldComponentUpdate(d,f,g):b.prototype&&b.prototype.isPureReactComponent?!Bb(c,d)||!Bb(e,f):!0}function Td(a,b,c){var d=!1,e=ra;var f=b.contextType;"object"===typeof f&&null!==f?f=T(f):(e=N(b)?Da:x.current,d=b.contextTypes,f=(d=null!==d&&void 0!==d)?Qa(a,e):ra);b=new b(c,f);a.memoizedState=null!==b.state&&void 0!==b.state?b.state:null;b.updater=Gb;a.stateNode=b;b._reactInternals=
a;d&&(a=a.stateNode,a.__reactInternalMemoizedUnmaskedChildContext=e,a.__reactInternalMemoizedMaskedChildContext=f);return b}function Ud(a,b,c,d){a=b.state;"function"===typeof b.componentWillReceiveProps&&b.componentWillReceiveProps(c,d);"function"===typeof b.UNSAFE_componentWillReceiveProps&&b.UNSAFE_componentWillReceiveProps(c,d);b.state!==a&&Gb.enqueueReplaceState(b,b.state,null)}function Ec(a,b,c,d){var e=a.stateNode;e.props=c;e.state=a.memoizedState;e.refs=Vd;Cc(a);var f=b.contextType;"object"===
typeof f&&null!==f?e.context=T(f):(f=N(b)?Da:x.current,e.context=Qa(a,f));e.state=a.memoizedState;f=b.getDerivedStateFromProps;"function"===typeof f&&(Dc(a,b,f,c),e.state=a.memoizedState);"function"===typeof b.getDerivedStateFromProps||"function"===typeof e.getSnapshotBeforeUpdate||"function"!==typeof e.UNSAFE_componentWillMount&&"function"!==typeof e.componentWillMount||(b=e.state,"function"===typeof e.componentWillMount&&e.componentWillMount(),"function"===typeof e.UNSAFE_componentWillMount&&e.UNSAFE_componentWillMount(),
b!==e.state&&Gb.enqueueReplaceState(e,e.state,null),Fb(a,c,e,d),e.state=a.memoizedState);"function"===typeof e.componentDidMount&&(a.flags|=4)}function bb(a,b,c){a=c.ref;if(null!==a&&"function"!==typeof a&&"object"!==typeof a){if(c._owner){c=c._owner;if(c){if(1!==c.tag)throw Error("Function components cannot have string refs. We recommend using useRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref");var d=c.stateNode}if(!d)throw Error("Missing owner for string ref "+
a+". This error is likely caused by a bug in React. Please file an issue.");var e=d,f=""+a;if(null!==b&&null!==b.ref&&"function"===typeof b.ref&&b.ref._stringRef===f)return b.ref;b=function(a){var b=e.refs;b===Vd&&(b=e.refs={});null===a?delete b[f]:b[f]=a};b._stringRef=f;return b}if("string"!==typeof a)throw Error("Expected ref to be a function, a string, an object returned by React.createRef(), or null.");if(!c._owner)throw Error("Element ref was specified as a string ("+a+") but no owner was set. This could happen for one of the following reasons:\n1. You may be adding a ref to a function component\n2. You may be adding a ref to a component that was not created inside a component's render method\n3. You have multiple copies of React loaded\nSee https://reactjs.org/link/refs-must-have-owner for more information.");
}return a}function Hb(a,b){a=Object.prototype.toString.call(b);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===a?"object with keys {"+Object.keys(b).join(", ")+"}":a)+"). If you meant to render a collection of children, use an array instead.");}function Wd(a){var b=a._init;return b(a._payload)}function Xd(a){function b(b,c){if(a){var d=b.deletions;null===d?(b.deletions=[c],b.flags|=16):d.push(c)}}function c(c,d){if(!a)return null;for(;null!==d;)b(c,d),d=d.sibling;
return null}function d(a,b){for(a=new Map;null!==b;)null!==b.key?a.set(b.key,b):a.set(b.index,b),b=b.sibling;return a}function e(a,b){a=ua(a,b);a.index=0;a.sibling=null;return a}function f(b,c,d){b.index=d;if(!a)return b.flags|=1048576,c;d=b.alternate;if(null!==d)return d=d.index,d<c?(b.flags|=2,c):d;b.flags|=2;return c}function g(b){a&&null===b.alternate&&(b.flags|=2);return b}function h(a,b,c,d){if(null===b||6!==b.tag)return b=Fc(c,a.mode,d),b.return=a,b;b=e(b,c);b.return=a;return b}function k(a,
b,c,d){var f=c.type;if(f===Na)return p(a,b,c.props.children,d,c.key);if(null!==b&&(b.elementType===f||"object"===typeof f&&null!==f&&f.$$typeof===pa&&Wd(f)===b.type))return d=e(b,c.props),d.ref=bb(a,b,c),d.return=a,d;d=Ib(c.type,c.key,c.props,null,a.mode,d);d.ref=bb(a,b,c);d.return=a;return d}function l(a,b,c,d){if(null===b||4!==b.tag||b.stateNode.containerInfo!==c.containerInfo||b.stateNode.implementation!==c.implementation)return b=Gc(c,a.mode,d),b.return=a,b;b=e(b,c.children||[]);b.return=a;return b}
function p(a,b,c,d,f){if(null===b||7!==b.tag)return b=Ha(c,a.mode,d,f),b.return=a,b;b=e(b,c);b.return=a;return b}function n(a,b,c){if("string"===typeof b&&""!==b||"number"===typeof b)return b=Fc(""+b,a.mode,c),b.return=a,b;if("object"===typeof b&&null!==b){switch(b.$$typeof){case Jb:return c=Ib(b.type,b.key,b.props,null,a.mode,c),c.ref=bb(a,null,b),c.return=a,c;case $a:return b=Gc(b,a.mode,c),b.return=a,b;case pa:var d=b._init;return n(a,d(b._payload),c)}if(cb(b)||Za(b))return b=Ha(b,a.mode,c,null),
b.return=a,b;Hb(a,b)}return null}function C(a,b,c,d){var e=null!==b?b.key:null;if("string"===typeof c&&""!==c||"number"===typeof c)return null!==e?null:h(a,b,""+c,d);if("object"===typeof c&&null!==c){switch(c.$$typeof){case Jb:return c.key===e?k(a,b,c,d):null;case $a:return c.key===e?l(a,b,c,d):null;case pa:return e=c._init,C(a,b,e(c._payload),d)}if(cb(c)||Za(c))return null!==e?null:p(a,b,c,d,null);Hb(a,c)}return null}function q(a,b,c,d,e){if("string"===typeof d&&""!==d||"number"===typeof d)return a=
a.get(c)||null,h(b,a,""+d,e);if("object"===typeof d&&null!==d){switch(d.$$typeof){case Jb:return a=a.get(null===d.key?c:d.key)||null,k(b,a,d,e);case $a:return a=a.get(null===d.key?c:d.key)||null,l(b,a,d,e);case pa:var f=d._init;return q(a,b,c,f(d._payload),e)}if(cb(d)||Za(d))return a=a.get(c)||null,p(b,a,d,e,null);Hb(b,d)}return null}function m(e,g,h,k){for(var l=null,p=null,t=g,v=g=0,y=null;null!==t&&v<h.length;v++){t.index>v?(y=t,t=null):y=t.sibling;var m=C(e,t,h[v],k);if(null===m){null===t&&(t=
y);break}a&&t&&null===m.alternate&&b(e,t);g=f(m,g,v);null===p?l=m:p.sibling=m;p=m;t=y}if(v===h.length)return c(e,t),l;if(null===t){for(;v<h.length;v++)t=n(e,h[v],k),null!==t&&(g=f(t,g,v),null===p?l=t:p.sibling=t,p=t);return l}for(t=d(e,t);v<h.length;v++)y=q(t,e,v,h[v],k),null!==y&&(a&&null!==y.alternate&&t.delete(null===y.key?v:y.key),g=f(y,g,v),null===p?l=y:p.sibling=y,p=y);a&&t.forEach(function(a){return b(e,a)});return l}function r(e,g,h,k){var l=Za(h);if("function"!==typeof l)throw Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");
h=l.call(h);if(null==h)throw Error("An iterable object provided no iterator.");for(var p=l=null,t=g,v=g=0,y=null,m=h.next();null!==t&&!m.done;v++,m=h.next()){t.index>v?(y=t,t=null):y=t.sibling;var r=C(e,t,m.value,k);if(null===r){null===t&&(t=y);break}a&&t&&null===r.alternate&&b(e,t);g=f(r,g,v);null===p?l=r:p.sibling=r;p=r;t=y}if(m.done)return c(e,t),l;if(null===t){for(;!m.done;v++,m=h.next())m=n(e,m.value,k),null!==m&&(g=f(m,g,v),null===p?l=m:p.sibling=m,p=m);return l}for(t=d(e,t);!m.done;v++,m=h.next())m=
q(t,e,v,m.value,k),null!==m&&(a&&null!==m.alternate&&t.delete(null===m.key?v:m.key),g=f(m,g,v),null===p?l=m:p.sibling=m,p=m);a&&t.forEach(function(a){return b(e,a)});return l}function u(a,d,f,h){"object"===typeof f&&null!==f&&f.type===Na&&null===f.key&&(f=f.props.children);if("object"===typeof f&&null!==f){switch(f.$$typeof){case Jb:a:{for(var k=f.key,l=d;null!==l;){if(l.key===k){k=f.type;if(k===Na){if(7===l.tag){c(a,l.sibling);d=e(l,f.props.children);d.return=a;a=d;break a}}else if(l.elementType===
k||"object"===typeof k&&null!==k&&k.$$typeof===pa&&Wd(k)===l.type){c(a,l.sibling);d=e(l,f.props);d.ref=bb(a,l,f);d.return=a;a=d;break a}c(a,l);break}else b(a,l);l=l.sibling}f.type===Na?(d=Ha(f.props.children,a.mode,h,f.key),d.return=a,a=d):(h=Ib(f.type,f.key,f.props,null,a.mode,h),h.ref=bb(a,d,f),h.return=a,a=h)}return g(a);case $a:a:{for(l=f.key;null!==d;){if(d.key===l)if(4===d.tag&&d.stateNode.containerInfo===f.containerInfo&&d.stateNode.implementation===f.implementation){c(a,d.sibling);d=e(d,f.children||
[]);d.return=a;a=d;break a}else{c(a,d);break}else b(a,d);d=d.sibling}d=Gc(f,a.mode,h);d.return=a;a=d}return g(a);case pa:return l=f._init,u(a,d,l(f._payload),h)}if(cb(f))return m(a,d,f,h);if(Za(f))return r(a,d,f,h);Hb(a,f)}return"string"===typeof f&&""!==f||"number"===typeof f?(f=""+f,null!==d&&6===d.tag?(c(a,d.sibling),d=e(d,f),d.return=a,a=d):(c(a,d),d=Fc(f,a.mode,h),d.return=a,a=d),g(a)):c(a,d)}return u}function Ia(a){if(a===db)throw Error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue.");
return a}function Hc(a,b){u(eb,b);u(fb,a);u(ea,db);a=Zd;q(ea);u(ea,a)}function Va(a){q(ea);q(fb);q(eb)}function $d(a){Ia(eb.current);var b=Ia(ea.current),c=Zd;b!==c&&(u(fb,a),u(ea,c))}function Ic(a){fb.current===a&&(q(ea),q(fb))}function Kb(a){for(var b=a;null!==b;){if(13===b.tag){var c=b.memoizedState;if(null!==c&&(null===c.dehydrated||ae()||Jc()))return b}else if(19===b.tag&&void 0!==b.memoizedProps.revealOrder){if(0!==(b.flags&128))return b}else if(null!==b.child){b.child.return=b;b=b.child;continue}if(b===
a)break;for(;null===b.sibling;){if(null===b.return||b.return===a)return null;b=b.return}b.sibling.return=b.return;b=b.sibling}return null}function Kc(){for(var a=0;a<Lc.length;a++)Lc[a]._workInProgressVersionSecondary=null;Lc.length=0}function K(){throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");
}function Mc(a,b){if(null===b)return!1;for(var c=0;c<b.length&&c<a.length;c++)if(!Z(a[c],b[c]))return!1;return!0}function Nc(a,b,c,d,e,f){Ja=f;r=b;b.memoizedState=null;b.updateQueue=null;b.lanes=0;Lb.current=null===a||null===a.memoizedState?tf:uf;a=c(d,e);if(gb){f=0;do{gb=!1;if(25<=f)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");f+=1;H=F=null;b.updateQueue=null;Lb.current=vf;a=c(d,e)}while(gb)}Lb.current=Mb;b=null!==F&&null!==F.next;Ja=0;H=F=r=
null;Nb=!1;if(b)throw Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");return a}function fa(){var a={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};null===H?r.memoizedState=H=a:H=H.next=a;return H}function V(){if(null===F){var a=r.alternate;a=null!==a?a.memoizedState:null}else a=F.next;var b=null===H?r.memoizedState:H.next;if(null!==b)H=b,F=a;else{if(null===a)throw Error("Rendered more hooks than during the previous render.");
F=a;a={memoizedState:F.memoizedState,baseState:F.baseState,baseQueue:F.baseQueue,queue:F.queue,next:null};null===H?r.memoizedState=H=a:H=H.next=a}return H}function hb(a,b){return"function"===typeof b?b(a):b}function Oc(a,b,c){b=V();c=b.queue;if(null===c)throw Error("Should have a queue. This is likely a bug in React. Please file an issue.");c.lastRenderedReducer=a;var d=F,e=d.baseQueue,f=c.pending;if(null!==f){if(null!==e){var g=e.next;e.next=f.next;f.next=g}d.baseQueue=e=f;c.pending=null}if(null!==
e){f=e.next;d=d.baseState;var h=g=null,k=null,l=f;do{var p=l.lane;if((Ja&p)===p)null!==k&&(k=k.next={lane:0,action:l.action,hasEagerState:l.hasEagerState,eagerState:l.eagerState,next:null}),d=l.hasEagerState?l.eagerState:a(d,l.action);else{var m={lane:p,action:l.action,hasEagerState:l.hasEagerState,eagerState:l.eagerState,next:null};null===k?(h=k=m,g=d):k=k.next=m;r.lanes|=p;U|=p}l=l.next}while(null!==l&&l!==f);null===k?g=d:k.next=h;Z(d,b.memoizedState)||(O=!0);b.memoizedState=d;b.baseState=g;b.baseQueue=
k;c.lastRenderedState=d}a=c.interleaved;if(null!==a){e=a;do f=e.lane,r.lanes|=f,U|=f,e=e.next;while(e!==a)}else null===e&&(c.lanes=0);return[b.memoizedState,c.dispatch]}function Pc(a,b,c){b=V();c=b.queue;if(null===c)throw Error("Should have a queue. This is likely a bug in React. Please file an issue.");c.lastRenderedReducer=a;var d=c.dispatch,e=c.pending,f=b.memoizedState;if(null!==e){c.pending=null;var g=e=e.next;do f=a(f,g.action),g=g.next;while(g!==e);Z(f,b.memoizedState)||(O=!0);b.memoizedState=
f;null===b.baseQueue&&(b.baseState=f);c.lastRenderedState=f}return[f,d]}function be(a,b,c){}function ce(a,b,c){c=r;var d=V(),e=b(),f=!Z(d.memoizedState,e);f&&(d.memoizedState=e,O=!0);d=d.queue;Qc(de.bind(null,c,d,a),[a]);if(d.getSnapshot!==b||f||null!==H&&H.memoizedState.tag&1){c.flags|=2048;ib(9,ee.bind(null,c,d,e,b),void 0,null);if(null===A)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");0!==(Ja&30)||fe(c,b,e)}return e}function fe(a,b,c){a.flags|=16384;
a={getSnapshot:b,value:c};b=r.updateQueue;null===b?(b={lastEffect:null,stores:null},r.updateQueue=b,b.stores=[a]):(c=b.stores,null===c?b.stores=[a]:c.push(a))}function ee(a,b,c,d){b.value=c;b.getSnapshot=d;ge(b)&&he(a)}function de(a,b,c){return c(function(){ge(b)&&he(a)})}function ge(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!Z(a,c)}catch(d){return!0}}function he(a){var b=Ua(a,1);null!==b&&va(b,a,1,-1)}function ie(a){var b=fa();"function"===typeof a&&(a=a());b.memoizedState=b.baseState=
a;a={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:hb,lastRenderedState:a};b.queue=a;a=a.dispatch=wf.bind(null,r,a);return[b.memoizedState,a]}function ib(a,b,c,d){a={tag:a,create:b,destroy:c,deps:d,next:null};b=r.updateQueue;null===b?(b={lastEffect:null,stores:null},r.updateQueue=b,b.lastEffect=a.next=a):(c=b.lastEffect,null===c?b.lastEffect=a.next=a:(d=c.next,c.next=a,a.next=d,b.lastEffect=a));return a}function je(a){return V().memoizedState}function Ob(a,b,c,d){var e=fa();
r.flags|=a;e.memoizedState=ib(1|b,c,void 0,void 0===d?null:d)}function Pb(a,b,c,d){var e=V();d=void 0===d?null:d;var f=void 0;if(null!==F){var g=F.memoizedState;f=g.destroy;if(null!==d&&Mc(d,g.deps)){e.memoizedState=ib(b,c,f,d);return}}r.flags|=a;e.memoizedState=ib(1|b,c,f,d)}function ke(a,b){return Ob(8390656,8,a,b)}function Qc(a,b){return Pb(2048,8,a,b)}function le(a,b){return Pb(4,2,a,b)}function me(a,b){return Pb(4,4,a,b)}function ne(a,b){if("function"===typeof b)return a=a(),b(a),function(){b(null)};
if(null!==b&&void 0!==b)return a=a(),b.current=a,function(){b.current=null}}function oe(a,b,c){c=null!==c&&void 0!==c?c.concat([a]):null;return Pb(4,4,ne.bind(null,b,a),c)}function Rc(a,b){}function pe(a,b){var c=V();b=void 0===b?null:b;var d=c.memoizedState;if(null!==d&&null!==b&&Mc(b,d[1]))return d[0];c.memoizedState=[a,b];return a}function qe(a,b){var c=V();b=void 0===b?null:b;var d=c.memoizedState;if(null!==d&&null!==b&&Mc(b,d[1]))return d[0];a=a();c.memoizedState=[a,b];return a}function re(a,
b,c){if(0===(Ja&21))return a.baseState&&(a.baseState=!1,O=!0),a.memoizedState=c;Z(c,b)||(c=Dd(),r.lanes|=c,U|=c,a.baseState=!0);return b}function xf(a,b,c){c=B;B=0!==c&&4>c?c:4;a(!0);var d=Sc.transition;Sc.transition={};try{a(!1),b()}finally{B=c,Sc.transition=d}}function se(){return V().memoizedState}function yf(a,b,c){var d=Wa(a);c={lane:d,action:c,hasEagerState:!1,eagerState:null,next:null};if(te(a))ue(b,c);else if(c=Od(a,b,c,d),null!==c){var e=ba();va(c,a,d,e);ve(c,b,d)}}function wf(a,b,c){var d=
Wa(a),e={lane:d,action:c,hasEagerState:!1,eagerState:null,next:null};if(te(a))ue(b,e);else{var f=a.alternate;if(0===a.lanes&&(null===f||0===f.lanes)&&(f=b.lastRenderedReducer,null!==f))try{var g=b.lastRenderedState,h=f(g,c);e.hasEagerState=!0;e.eagerState=h;if(Z(h,g)){var k=b.interleaved;null===k?(e.next=e,Bc(b)):(e.next=k.next,k.next=e);b.interleaved=e;return}}catch(l){}finally{}c=Od(a,b,e,d);null!==c&&(e=ba(),va(c,a,d,e),ve(c,b,d))}}function te(a){var b=a.alternate;return a===r||null!==b&&b===r}
function ue(a,b){gb=Nb=!0;var c=a.pending;null===c?b.next=b:(b.next=c.next,c.next=b);a.pending=b}function ve(a,b,c){if(0!==(c&4194240)){var d=b.lanes;d&=a.pendingLanes;c|=d;b.lanes=c;Ed(a,c)}}function Tc(a,b){try{var c="",d=b;do c+=qf(d),d=d.return;while(d);var e=c}catch(f){e="\nError generating stack: "+f.message+"\n"+f.stack}return{value:a,source:b,stack:e,digest:null}}function Uc(a,b,c){return{value:a,source:null,stack:null!=c?c:null,digest:null!=b?b:null}}function Vc(a,b){try{console.error(b.value)}catch(c){setTimeout(function(){throw c;
})}}function we(a,b,c){c=sa(-1,c);c.tag=3;c.payload={element:null};var d=b.value;c.callback=function(){Qb||(Qb=!0,Wc=d);Vc(a,b)};return c}function xe(a,b,c){c=sa(-1,c);c.tag=3;var d=a.type.getDerivedStateFromError;if("function"===typeof d){var e=b.value;c.payload=function(){return d(e)};c.callback=function(){Vc(a,b)}}var f=a.stateNode;null!==f&&"function"===typeof f.componentDidCatch&&(c.callback=function(){Vc(a,b);"function"!==typeof d&&(null===wa?wa=new Set([this]):wa.add(this));var c=b.stack;this.componentDidCatch(b.value,
{componentStack:null!==c?c:""})});return c}function ye(a,b,c){var d=a.pingCache;if(null===d){d=a.pingCache=new zf;var e=new Set;d.set(b,e)}else e=d.get(b),void 0===e&&(e=new Set,d.set(b,e));e.has(c)||(e.add(c),a=Af.bind(null,a,b,c),b.then(a,a))}function P(a,b,c,d){b.child=null===a?Bf(b,null,c,d):Xa(b,a.child,c,d)}function ze(a,b,c,d,e){c=c.render;var f=b.ref;Ta(b,e);d=Nc(a,b,c,d,f,e);if(null!==a&&!O)return b.updateQueue=a.updateQueue,b.flags&=-2053,a.lanes&=~e,ma(a,b,e);b.flags|=1;P(a,b,d,e);return b.child}
function Ae(a,b,c,d,e){if(null===a){var f=c.type;if("function"===typeof f&&!Xc(f)&&void 0===f.defaultProps&&null===c.compare&&void 0===c.defaultProps)return b.tag=15,b.type=f,Be(a,b,f,d,e);a=Ib(c.type,null,d,b,b.mode,e);a.ref=b.ref;a.return=b;return b.child=a}f=a.child;if(0===(a.lanes&e)){var g=f.memoizedProps;c=c.compare;c=null!==c?c:Bb;if(c(g,d)&&a.ref===b.ref)return ma(a,b,e)}b.flags|=1;a=ua(f,d);a.ref=b.ref;a.return=b;return b.child=a}function Be(a,b,c,d,e){if(null!==a){var f=a.memoizedProps;
if(Bb(f,d)&&a.ref===b.ref)if(O=!1,b.pendingProps=d=f,0!==(a.lanes&e))0!==(a.flags&131072)&&(O=!0);else return b.lanes=a.lanes,ma(a,b,e)}return Yc(a,b,c,d,e)}function Ce(a,b,c){var d=b.pendingProps,e=d.children,f=null!==a?a.memoizedState:null;if("hidden"===d.mode)if(0===(b.mode&1))b.memoizedState={baseLanes:0,cachePool:null,transitions:null},u(ha,M),M|=c;else{if(0===(c&1073741824))return a=null!==f?f.baseLanes|c:c,b.lanes=b.childLanes=1073741824,b.memoizedState={baseLanes:a,cachePool:null,transitions:null},
b.updateQueue=null,u(ha,M),M|=a,null;b.memoizedState={baseLanes:0,cachePool:null,transitions:null};d=null!==f?f.baseLanes:c;u(ha,M);M|=d}else null!==f?(d=f.baseLanes|c,b.memoizedState=null):d=c,u(ha,M),M|=d;P(a,b,e,c);return b.child}function De(a,b){var c=b.ref;if(null===a&&null!==c||null!==a&&a.ref!==c)b.flags|=512}function Yc(a,b,c,d,e){var f=N(c)?Da:x.current;f=Qa(b,f);Ta(b,e);c=Nc(a,b,c,d,f,e);if(null!==a&&!O)return b.updateQueue=a.updateQueue,b.flags&=-2053,a.lanes&=~e,ma(a,b,e);b.flags|=1;P(a,
b,c,e);return b.child}function Ee(a,b,c,d,e){if(N(c)){var f=!0;xb(b)}else f=!1;Ta(b,e);if(null===b.stateNode)Rb(a,b),Td(b,c,d),Ec(b,c,d,e),d=!0;else if(null===a){var g=b.stateNode,h=b.memoizedProps;g.props=h;var k=g.context,l=c.contextType;"object"===typeof l&&null!==l?l=T(l):(l=N(c)?Da:x.current,l=Qa(b,l));var p=c.getDerivedStateFromProps,m="function"===typeof p||"function"===typeof g.getSnapshotBeforeUpdate;m||"function"!==typeof g.UNSAFE_componentWillReceiveProps&&"function"!==typeof g.componentWillReceiveProps||
(h!==d||k!==l)&&Ud(b,g,d,l);ta=!1;var n=b.memoizedState;g.state=n;Fb(b,d,g,e);k=b.memoizedState;h!==d||n!==k||E.current||ta?("function"===typeof p&&(Dc(b,c,p,d),k=b.memoizedState),(h=ta||Sd(b,c,h,d,n,k,l))?(m||"function"!==typeof g.UNSAFE_componentWillMount&&"function"!==typeof g.componentWillMount||("function"===typeof g.componentWillMount&&g.componentWillMount(),"function"===typeof g.UNSAFE_componentWillMount&&g.UNSAFE_componentWillMount()),"function"===typeof g.componentDidMount&&(b.flags|=4)):
("function"===typeof g.componentDidMount&&(b.flags|=4),b.memoizedProps=d,b.memoizedState=k),g.props=d,g.state=k,g.context=l,d=h):("function"===typeof g.componentDidMount&&(b.flags|=4),d=!1)}else{g=b.stateNode;Pd(a,b);h=b.memoizedProps;l=b.type===b.elementType?h:aa(b.type,h);g.props=l;m=b.pendingProps;n=g.context;k=c.contextType;"object"===typeof k&&null!==k?k=T(k):(k=N(c)?Da:x.current,k=Qa(b,k));var q=c.getDerivedStateFromProps;(p="function"===typeof q||"function"===typeof g.getSnapshotBeforeUpdate)||
"function"!==typeof g.UNSAFE_componentWillReceiveProps&&"function"!==typeof g.componentWillReceiveProps||(h!==m||n!==k)&&Ud(b,g,d,k);ta=!1;n=b.memoizedState;g.state=n;Fb(b,d,g,e);var r=b.memoizedState;h!==m||n!==r||E.current||ta?("function"===typeof q&&(Dc(b,c,q,d),r=b.memoizedState),(l=ta||Sd(b,c,l,d,n,r,k)||!1)?(p||"function"!==typeof g.UNSAFE_componentWillUpdate&&"function"!==typeof g.componentWillUpdate||("function"===typeof g.componentWillUpdate&&g.componentWillUpdate(d,r,k),"function"===typeof g.UNSAFE_componentWillUpdate&&
g.UNSAFE_componentWillUpdate(d,r,k)),"function"===typeof g.componentDidUpdate&&(b.flags|=4),"function"===typeof g.getSnapshotBeforeUpdate&&(b.flags|=1024)):("function"!==typeof g.componentDidUpdate||h===a.memoizedProps&&n===a.memoizedState||(b.flags|=4),"function"!==typeof g.getSnapshotBeforeUpdate||h===a.memoizedProps&&n===a.memoizedState||(b.flags|=1024),b.memoizedProps=d,b.memoizedState=r),g.props=d,g.state=r,g.context=k,d=l):("function"!==typeof g.componentDidUpdate||h===a.memoizedProps&&n===
a.memoizedState||(b.flags|=4),"function"!==typeof g.getSnapshotBeforeUpdate||h===a.memoizedProps&&n===a.memoizedState||(b.flags|=1024),d=!1)}return Zc(a,b,c,d,f,e)}function Zc(a,b,c,d,e,f){De(a,b);var g=0!==(b.flags&128);if(!d&&!g)return e&&Ld(b,c,!1),ma(a,b,f);d=b.stateNode;Cf.current=b;var h=g&&"function"!==typeof c.getDerivedStateFromError?null:d.render();b.flags|=1;null!==a&&g?(b.child=Xa(b,a.child,null,f),b.child=Xa(b,null,h,f)):P(a,b,h,f);b.memoizedState=d.state;e&&Ld(b,c,!0);return b.child}
function Fe(a){var b=a.stateNode;b.pendingContext?Jd(a,b.pendingContext,b.pendingContext!==b.context):b.context&&Jd(a,b.context,!1);Hc(a,b.containerInfo)}function $c(a){return{baseLanes:a,cachePool:null,transitions:null}}function Ge(a,b,c){var d=b.pendingProps,e=w.current,f=!1,g=0!==(b.flags&128),h;(h=g)||(h=null!==a&&null===a.memoizedState?!1:0!==(e&2));if(h)f=!0,b.flags&=-129;else if(null===a||null!==a.memoizedState)e|=1;u(w,e&1);if(null===a){a=b.memoizedState;if(null!==a&&null!==a.dehydrated)return 0===
(b.mode&1)?b.lanes=1:Jc()?b.lanes=8:b.lanes=1073741824,null;g=d.children;a=d.fallback;return f?(d=b.mode,f=b.child,g={mode:"hidden",children:g},0===(d&1)&&null!==f?(f.childLanes=0,f.pendingProps=g):f=Sb(g,d,0,null),a=Ha(a,d,c,null),f.return=b,a.return=b,f.sibling=a,b.child=f,b.child.memoizedState=$c(c),b.memoizedState=ad,a):bd(b,g)}e=a.memoizedState;if(null!==e&&(h=e.dehydrated,null!==h))return Df(a,b,g,d,h,e,c);if(f){f=d.fallback;g=b.mode;e=a.child;h=e.sibling;var k={mode:"hidden",children:d.children};
0===(g&1)&&b.child!==e?(d=b.child,d.childLanes=0,d.pendingProps=k,b.deletions=null):(d=ua(e,k),d.subtreeFlags=e.subtreeFlags&14680064);null!==h?f=ua(h,f):(f=Ha(f,g,c,null),f.flags|=2);f.return=b;d.return=b;d.sibling=f;b.child=d;d=f;f=b.child;g=a.child.memoizedState;g=null===g?$c(c):{baseLanes:g.baseLanes|c,cachePool:null,transitions:g.transitions};f.memoizedState=g;f.childLanes=a.childLanes&~c;b.memoizedState=ad;return d}f=a.child;a=f.sibling;d=ua(f,{mode:"visible",children:d.children});0===(b.mode&
1)&&(d.lanes=c);d.return=b;d.sibling=null;null!==a&&(c=b.deletions,null===c?(b.deletions=[a],b.flags|=16):c.push(a));b.child=d;b.memoizedState=null;return d}function bd(a,b,c){b=Sb({mode:"visible",children:b},a.mode,0,null);b.return=a;return a.child=b}function Tb(a,b,c,d){null!==d&&(null===na?na=[d]:na.push(d));Xa(b,a.child,null,c);a=bd(b,b.pendingProps.children);a.flags|=2;b.memoizedState=null;return a}function Df(a,b,c,d,e,f,g){if(c){if(b.flags&256)return b.flags&=-257,f=Uc(Error("There was an error while hydrating this Suspense boundary. Switched to client rendering.")),
Tb(a,b,g,f);if(null!==b.memoizedState)return b.child=a.child,b.flags|=128,null;f=d.fallback;c=b.mode;d=Sb({mode:"visible",children:d.children},c,0,null);f=Ha(f,c,g,null);f.flags|=2;d.return=b;f.return=b;d.sibling=f;b.child=d;0!==(b.mode&1)&&Xa(b,a.child,null,g);b.child.memoizedState=$c(g);b.memoizedState=ad;return f}if(0===(b.mode&1))return Tb(a,b,g,null);if(Jc())return f=Ef().digest,f=Uc(Error("The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering."),
f,void 0),Tb(a,b,g,f);c=0!==(g&a.childLanes);if(O||c){d=A;if(null!==d){switch(g&-g){case 4:c=2;break;case 16:c=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:c=32;break;case 536870912:c=268435456;break;default:c=0}c=0!==(c&(d.suspendedLanes|g))?0:c;0!==c&&c!==f.retryLane&&(f.retryLane=c,Ua(a,c),va(d,a,
c,-1))}cd();f=Uc(Error("This Suspense boundary received an update before it finished hydrating. This caused the boundary to switch to client rendering. The usual way to fix this is to wrap the original update in startTransition."));return Tb(a,b,g,f)}if(ae())return b.flags|=128,b.child=a.child,Ff.bind(null,a),Gf(),null;a=bd(b,d.children);a.flags|=4096;return a}function He(a,b,c){a.lanes|=b;var d=a.alternate;null!==d&&(d.lanes|=b);Ac(a.return,b,c)}function dd(a,b,c,d,e){var f=a.memoizedState;null===
f?a.memoizedState={isBackwards:b,rendering:null,renderingStartTime:0,last:d,tail:c,tailMode:e}:(f.isBackwards=b,f.rendering=null,f.renderingStartTime=0,f.last=d,f.tail=c,f.tailMode=e)}function Ie(a,b,c){var d=b.pendingProps,e=d.revealOrder,f=d.tail;P(a,b,d.children,c);d=w.current;if(0!==(d&2))d=d&1|2,b.flags|=128;else{if(null!==a&&0!==(a.flags&128))a:for(a=b.child;null!==a;){if(13===a.tag)null!==a.memoizedState&&He(a,c,b);else if(19===a.tag)He(a,c,b);else if(null!==a.child){a.child.return=a;a=a.child;
continue}if(a===b)break a;for(;null===a.sibling;){if(null===a.return||a.return===b)break a;a=a.return}a.sibling.return=a.return;a=a.sibling}d&=1}u(w,d);if(0===(b.mode&1))b.memoizedState=null;else switch(e){case "forwards":c=b.child;for(e=null;null!==c;)a=c.alternate,null!==a&&null===Kb(a)&&(e=c),c=c.sibling;c=e;null===c?(e=b.child,b.child=null):(e=c.sibling,c.sibling=null);dd(b,!1,e,c,f);break;case "backwards":c=null;e=b.child;for(b.child=null;null!==e;){a=e.alternate;if(null!==a&&null===Kb(a)){b.child=
e;break}a=e.sibling;e.sibling=c;c=e;e=a}dd(b,!0,c,null,f);break;case "together":dd(b,!1,null,null,void 0);break;default:b.memoizedState=null}return b.child}function Rb(a,b){0===(b.mode&1)&&null!==a&&(a.alternate=null,b.alternate=null,b.flags|=2)}function ma(a,b,c){null!==a&&(b.dependencies=a.dependencies);U|=b.lanes;if(0===(c&b.childLanes))return null;if(null!==a&&b.child!==a.child)throw Error("Resuming work not yet implemented.");if(null!==b.child){a=b.child;c=ua(a,a.pendingProps);b.child=c;for(c.return=
b;null!==a.sibling;)a=a.sibling,c=c.sibling=ua(a,a.pendingProps),c.return=b;c.sibling=null}return b.child}function Hf(a,b,c){switch(b.tag){case 3:Fe(b);break;case 5:$d(b);break;case 1:N(b.type)&&xb(b);break;case 4:Hc(b,b.stateNode.containerInfo);break;case 10:var d=b.type._context,e=b.memoizedProps.value;u(Db,d._currentValue2);d._currentValue2=e;break;case 13:d=b.memoizedState;if(null!==d){if(null!==d.dehydrated)return u(w,w.current&1),b.flags|=128,null;if(0!==(c&b.child.childLanes))return Ge(a,b,
c);u(w,w.current&1);a=ma(a,b,c);return null!==a?a.sibling:null}u(w,w.current&1);break;case 19:d=0!==(c&b.childLanes);if(0!==(a.flags&128)){if(d)return Ie(a,b,c);b.flags|=128}e=b.memoizedState;null!==e&&(e.rendering=null,e.tail=null,e.lastEffect=null);u(w,w.current);if(d)break;else return null;case 22:case 23:return b.lanes=0,Ce(a,b,c)}return ma(a,b,c)}function jb(a,b){switch(a.tailMode){case "hidden":b=a.tail;for(var c=null;null!==b;)null!==b.alternate&&(c=b),b=b.sibling;null===c?a.tail=null:c.sibling=
null;break;case "collapsed":c=a.tail;for(var d=null;null!==c;)null!==c.alternate&&(d=c),c=c.sibling;null===d?b||null===a.tail?a.tail=null:a.tail.sibling=null:d.sibling=null}}function L(a){var b=null!==a.alternate&&a.alternate.child===a.child,c=0,d=0;if(b)for(var e=a.child;null!==e;)c|=e.lanes|e.childLanes,d|=e.subtreeFlags&14680064,d|=e.flags&14680064,e.return=a,e=e.sibling;else for(e=a.child;null!==e;)c|=e.lanes|e.childLanes,d|=e.subtreeFlags,d|=e.flags,e.return=a,e=e.sibling;a.subtreeFlags|=d;a.childLanes=
c;return b}function If(a,b,c){var d=b.pendingProps;vc(b);switch(b.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return L(b),null;case 1:return N(b.type)&&(q(E),q(x)),L(b),null;case 3:return c=b.stateNode,Va(),q(E),q(x),Kc(),c.pendingContext&&(c.context=c.pendingContext,c.pendingContext=null),null!==a&&null!==a.child||null===a||a.memoizedState.isDehydrated&&0===(b.flags&256)||(b.flags|=1024,null!==na&&(ed(na),na=null)),Je(a,b),L(b),null;case 5:Ic(b);c=Ia(eb.current);
var e=b.type;if(null!==a&&null!=b.stateNode)Jf(a,b,e,d,c),a.ref!==b.ref&&(b.flags|=512);else{if(!d){if(null===b.stateNode)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");L(b);return null}Ia(ea.current);a={type:e,props:d,isHidden:!1,children:[],internalInstanceHandle:b,rootContainerInstance:c,tag:"INSTANCE"};Kf(a,b,!1,!1);b.stateNode=a;null!==b.ref&&(b.flags|=512)}L(b);return null;case 6:if(a&&null!=b.stateNode)Lf(a,b,a.memoizedProps,
d);else{if("string"!==typeof d&&null===b.stateNode)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");Ia(eb.current);Ia(ea.current);b.stateNode={text:d,isHidden:!1,tag:"TEXT"}}L(b);return null;case 13:q(w);d=b.memoizedState;if(null===a||null!==a.memoizedState&&null!==a.memoizedState.dehydrated){if(null!==d&&null!==d.dehydrated){if(null===a){throw Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");
throw Error("Expected prepareToHydrateHostSuspenseInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");}0===(b.flags&128)&&(b.memoizedState=null);b.flags|=4;L(b);e=!1}else null!==na&&(ed(na),na=null),e=!0;if(!e)return b.flags&65536?b:null}if(0!==(b.flags&128))return b.lanes=c,b;c=null!==d;c!==(null!==a&&null!==a.memoizedState)&&c&&(b.child.flags|=8192,0!==(b.mode&1)&&(null===a||0!==(w.current&1)?0===G&&(G=3):cd()));null!==b.updateQueue&&(b.flags|=4);
L(b);return null;case 4:return Va(),Je(a,b),L(b),null;case 10:return zc(b.type._context),L(b),null;case 17:return N(b.type)&&(q(E),q(x)),L(b),null;case 19:q(w);e=b.memoizedState;if(null===e)return L(b),null;d=0!==(b.flags&128);var f=e.rendering;if(null===f)if(d)jb(e,!1);else{if(0!==G||null!==a&&0!==(a.flags&128))for(a=b.child;null!==a;){f=Kb(a);if(null!==f){b.flags|=128;jb(e,!1);a=f.updateQueue;null!==a&&(b.updateQueue=a,b.flags|=4);b.subtreeFlags=0;a=c;for(c=b.child;null!==c;)d=c,e=a,d.flags&=14680066,
f=d.alternate,null===f?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=f.childLanes,d.lanes=f.lanes,d.child=f.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=f.memoizedProps,d.memoizedState=f.memoizedState,d.updateQueue=f.updateQueue,d.type=f.type,e=f.dependencies,d.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),c=c.sibling;u(w,w.current&1|2);return b.child}a=
a.sibling}null!==e.tail&&z()>kb&&(b.flags|=128,d=!0,jb(e,!1),b.lanes=4194304)}else{if(!d)if(a=Kb(f),null!==a){if(b.flags|=128,d=!0,a=a.updateQueue,null!==a&&(b.updateQueue=a,b.flags|=4),jb(e,!0),null===e.tail&&"hidden"===e.tailMode&&!f.alternate)return L(b),null}else 2*z()-e.renderingStartTime>kb&&1073741824!==c&&(b.flags|=128,d=!0,jb(e,!1),b.lanes=4194304);e.isBackwards?(f.sibling=b.child,b.child=f):(a=e.last,null!==a?a.sibling=f:b.child=f,e.last=f)}if(null!==e.tail)return b=e.tail,e.rendering=b,
e.tail=b.sibling,e.renderingStartTime=z(),b.sibling=null,a=w.current,u(w,d?a&1|2:a&1),b;L(b);return null;case 22:case 23:return M=ha.current,q(ha),c=null!==b.memoizedState,null!==a&&null!==a.memoizedState!==c&&(b.flags|=8192),c&&0!==(b.mode&1)?0!==(M&1073741824)&&(L(b),b.subtreeFlags&6&&(b.flags|=8192)):L(b),null;case 24:return null;case 25:return null}throw Error("Unknown unit of work tag ("+b.tag+"). This error is likely caused by a bug in React. Please file an issue.");}function Mf(a,b,c){vc(b);
switch(b.tag){case 1:return N(b.type)&&(q(E),q(x)),a=b.flags,a&65536?(b.flags=a&-65537|128,b):null;case 3:return Va(),q(E),q(x),Kc(),a=b.flags,0!==(a&65536)&&0===(a&128)?(b.flags=a&-65537|128,b):null;case 5:return Ic(b),null;case 13:q(w);a=b.memoizedState;if(null!==a&&null!==a.dehydrated&&null===b.alternate)throw Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");a=b.flags;return a&65536?(b.flags=a&-65537|128,b):null;case 19:return q(w),null;
case 4:return Va(),null;case 10:return zc(b.type._context),null;case 22:case 23:return M=ha.current,q(ha),null;case 24:return null;default:return null}}function Ub(a,b){var c=a.ref;if(null!==c)if("function"===typeof c)try{c(null)}catch(d){Q(a,b,d)}else c.current=null}function fd(a,b,c){try{c()}catch(d){Q(a,b,d)}}function Nf(a,b){for(n=b;null!==n;)if(a=n,b=a.child,0!==(a.subtreeFlags&1028)&&null!==b)b.return=a,n=b;else for(;null!==n;){a=n;try{var c=a.alternate;if(0!==(a.flags&1024))switch(a.tag){case 0:case 11:case 15:break;
case 1:if(null!==c){var d=c.memoizedProps,e=c.memoizedState,f=a.stateNode,g=f.getSnapshotBeforeUpdate(a.elementType===a.type?d:aa(a.type,d),e);f.__reactInternalSnapshotBeforeUpdate=g}break;case 3:a.stateNode.containerInfo.children.splice(0);break;case 5:case 6:case 4:case 17:break;default:throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.");}}catch(h){Q(a,a.return,h)}b=a.sibling;if(null!==b){b.return=a.return;n=b;break}n=
a.return}c=Ke;Ke=!1;return c}function Vb(a,b,c){var d=b.updateQueue;d=null!==d?d.lastEffect:null;if(null!==d){var e=d=d.next;do{if((e.tag&a)===a){var f=e.destroy;e.destroy=void 0;void 0!==f&&fd(b,c,f)}e=e.next}while(e!==d)}}function gd(a,b){b=b.updateQueue;b=null!==b?b.lastEffect:null;if(null!==b){var c=b=b.next;do{if((c.tag&a)===a){var d=c.create;c.destroy=d()}c=c.next}while(c!==b)}}function Le(a){var b=a.alternate;null!==b&&(a.alternate=null,Le(b));a.child=null;a.deletions=null;a.sibling=null;a.stateNode=
null;a.return=null;a.dependencies=null;a.memoizedProps=null;a.memoizedState=null;a.pendingProps=null;a.stateNode=null;a.updateQueue=null}function Me(a){return 5===a.tag||3===a.tag||4===a.tag}function Ne(a){a:for(;;){for(;null===a.sibling;){if(null===a.return||Me(a.return))return null;a=a.return}a.sibling.return=a.return;for(a=a.sibling;5!==a.tag&&6!==a.tag&&18!==a.tag;){if(a.flags&2)continue a;if(null===a.child||4===a.tag)continue a;else a.child.return=a,a=a.child}if(!(a.flags&2))return a.stateNode}}
function hd(a,b,c){var d=a.tag;if(5===d||6===d)a=a.stateNode,b?Of(c,a,b):Pf(c,a);else if(4!==d&&(a=a.child,null!==a))for(hd(a,b,c),a=a.sibling;null!==a;)hd(a,b,c),a=a.sibling}function id(a,b,c){var d=a.tag;if(5===d||6===d)a=a.stateNode,b?Hd(c,a,b):Gd(c,a);else if(4!==d&&(a=a.child,null!==a))for(id(a,b,c),a=a.sibling;null!==a;)id(a,b,c),a=a.sibling}function Ka(a,b,c){for(c=c.child;null!==c;)Oe(a,b,c),c=c.sibling}function Oe(a,b,c){if(da&&"function"===typeof da.onCommitFiberUnmount)try{da.onCommitFiberUnmount(rb,
c)}catch(h){}switch(c.tag){case 5:Ub(c,b);case 6:var d=I,e=ca;I=null;Ka(a,b,c);I=d;ca=e;null!==I&&(ca?Qf(I,c.stateNode):Id(I,c.stateNode));break;case 18:null!==I&&(ca?Rf(I,c.stateNode):Sf(I,c.stateNode));break;case 4:d=I;e=ca;I=c.stateNode.containerInfo;ca=!0;Ka(a,b,c);I=d;ca=e;break;case 0:case 11:case 14:case 15:d=c.updateQueue;if(null!==d&&(d=d.lastEffect,null!==d)){e=d=d.next;do{var f=e,g=f.destroy;f=f.tag;void 0!==g&&(0!==(f&2)?fd(c,b,g):0!==(f&4)&&fd(c,b,g));e=e.next}while(e!==d)}Ka(a,b,c);
break;case 1:Ub(c,b);d=c.stateNode;if("function"===typeof d.componentWillUnmount)try{d.props=c.memoizedProps,d.state=c.memoizedState,d.componentWillUnmount()}catch(h){Q(c,b,h)}Ka(a,b,c);break;case 21:Ka(a,b,c);break;case 22:Ka(a,b,c);break;default:Ka(a,b,c)}}function Pe(a){var b=a.updateQueue;if(null!==b){a.updateQueue=null;var c=a.stateNode;null===c&&(c=a.stateNode=new Tf);b.forEach(function(b){var d=Uf.bind(null,a,b);c.has(b)||(c.add(b),b.then(d,d))})}}function ia(a,b,c){c=b.deletions;if(null!==
c)for(var d=0;d<c.length;d++){var e=c[d];try{var f=a,g=b,h=g;a:for(;null!==h;){switch(h.tag){case 5:I=h.stateNode;ca=!1;break a;case 3:I=h.stateNode.containerInfo;ca=!0;break a;case 4:I=h.stateNode.containerInfo;ca=!0;break a}h=h.return}if(null===I)throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");Oe(f,g,e);I=null;ca=!1;var k=e.alternate;null!==k&&(k.return=null);e.return=null}catch(l){Q(e,b,l)}}if(b.subtreeFlags&12854)for(b=b.child;null!==
b;)Qe(b,a),b=b.sibling}function Qe(a,b,c){var d=a.alternate;c=a.flags;switch(a.tag){case 0:case 11:case 14:case 15:ia(b,a);ja(a);if(c&4){try{Vb(3,a,a.return),gd(3,a)}catch(f){Q(a,a.return,f)}try{Vb(5,a,a.return)}catch(f){Q(a,a.return,f)}}break;case 1:ia(b,a);ja(a);c&512&&null!==d&&Ub(d,d.return);break;case 5:ia(b,a);ja(a);c&512&&null!==d&&Ub(d,d.return);if(c&4){var e=a.stateNode;if(null!=e&&(c=a.memoizedProps,b=a.type,d=a.updateQueue,a.updateQueue=null,null!==d))try{e.type=b,e.props=c}catch(f){Q(a,
a.return,f)}}break;case 6:ia(b,a);ja(a);if(c&4){if(null===a.stateNode)throw Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");e=a.stateNode;c=a.memoizedProps;try{e.text=c}catch(f){Q(a,a.return,f)}}break;case 3:ia(b,a);ja(a);break;case 4:ia(b,a);ja(a);break;case 13:ia(b,a);ja(a);e=a.child;e.flags&8192&&(b=null!==e.memoizedState,e.stateNode.isHidden=b,!b||null!==e.alternate&&null!==e.alternate.memoizedState||(jd=z()));c&4&&Pe(a);
break;case 22:ia(b,a);ja(a);if(c&8192)a:for(c=null!==a.memoizedState,a.stateNode.isHidden=c,b=null,d=a;;){if(5===d.tag){if(null===b){b=d;try{e=d.stateNode,c?e.isHidden=!0:d.stateNode.isHidden=!1}catch(f){Q(a,a.return,f)}}}else if(6===d.tag){if(null===b)try{d.stateNode.isHidden=c?!0:!1}catch(f){Q(a,a.return,f)}}else if((22!==d.tag&&23!==d.tag||null===d.memoizedState||d===a)&&null!==d.child){d.child.return=d;d=d.child;continue}if(d===a)break a;for(;null===d.sibling;){if(null===d.return||d.return===
a)break a;b===d&&(b=null);d=d.return}b===d&&(b=null);d.sibling.return=d.return;d=d.sibling}break;case 19:ia(b,a);ja(a);c&4&&Pe(a);break;case 21:break;default:ia(b,a),ja(a)}}function ja(a){var b=a.flags;if(b&2){try{a:{for(var c=a.return;null!==c;){if(Me(c)){var d=c;break a}c=c.return}throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");}switch(d.tag){case 5:var e=d.stateNode;d.flags&32&&(d.flags&=-33);var f=Ne(a);id(a,f,e);break;case 3:case 4:var g=
d.stateNode.containerInfo,h=Ne(a);hd(a,h,g);break;default:throw Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.");}}catch(k){Q(a,a.return,k)}a.flags&=-3}b&4096&&(a.flags&=-4097)}function Vf(a,b,c){for(n=a;null!==n;)if(b=n,c=b.child,0!==(b.subtreeFlags&8772)&&null!==c)c.return=b,n=c;else for(b=a;null!==n;){c=n;if(0!==(c.flags&8772)){var d=c.alternate;try{if(0!==(c.flags&8772))switch(c.tag){case 0:case 11:case 15:gd(5,c);break;case 1:var e=c.stateNode;
if(c.flags&4)if(null===d)e.componentDidMount();else{var f=c.elementType===c.type?d.memoizedProps:aa(c.type,d.memoizedProps);e.componentDidUpdate(f,d.memoizedState,e.__reactInternalSnapshotBeforeUpdate)}var g=c.updateQueue;null!==g&&Rd(c,g,e);break;case 3:var h=c.updateQueue;if(null!==h){d=null;if(null!==c.child)switch(c.child.tag){case 5:d=wb(c.child.stateNode);break;case 1:d=c.child.stateNode}Rd(c,h,d)}break;case 5:break;case 6:break;case 4:break;case 12:break;case 13:break;case 19:case 17:case 21:case 22:case 23:case 25:break;
default:throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.");}if(c.flags&512){d=void 0;var k=c.ref;if(null!==k){var l=c.stateNode;switch(c.tag){case 5:d=wb(l);break;default:d=l}"function"===typeof k?k(d):k.current=d}}}catch(p){Q(c,c.return,p)}}if(c===b){n=null;break}d=c.sibling;if(null!==d){d.return=c.return;n=d;break}n=c.return}}function ba(){return 0!==(m&6)?z():-1!==Wb?Wb:Wb=z()}function Wa(a){if(0===(a.mode&1))return 1;
if(0!==(m&2)&&0!==J)return J&-J;if(null!==Wf.transition)return 0===Xb&&(Xb=Dd()),Xb;a=B;return 0!==a?a:16}function va(a,b,c,d){if(50<lb)throw lb=0,kd=null,Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");vb(a,c,d);if(0===(m&2)||a!==A)a===A&&(0===(m&2)&&(Yb|=c),4===G&&xa(a,J)),W(a,d),1===c&&0===m&&0===(b.mode&1)&&(kb=z()+500,yb&&Ea())}
function W(a,b){for(var c=a.callbackNode,d=a.suspendedLanes,e=a.pingedLanes,f=a.expirationTimes,g=a.pendingLanes;0<g;){var h=31-Ba(g),k=1<<h,l=f[h];if(-1===l){if(0===(k&d)||0!==(k&e))f[h]=lf(k,b)}else l<=b&&(a.expiredLanes|=k);g&=~k}d=tb(a,a===A?J:0);if(0===d)null!==c&&Re(c),a.callbackNode=null,a.callbackPriority=0;else if(b=d&-d,a.callbackPriority!==b){null!=c&&Re(c);if(1===b)0===a.tag?(c=Se.bind(null,a),yb=!0,null===S?S=[c]:S.push(c)):(c=Se.bind(null,a),null===S?S=[c]:S.push(c)),tc(uc,Ea),c=null;
else{switch(Fd(d)){case 1:c=uc;break;case 4:c=Xf;break;case 16:c=ld;break;case 536870912:c=Yf;break;default:c=ld}c=Te(c,Ue.bind(null,a))}a.callbackPriority=b;a.callbackNode=c}}function Ue(a,b){Wb=-1;Xb=0;if(0!==(m&6))throw Error("Should not already be working.");var c=a.callbackNode;if(Ya()&&a.callbackNode!==c)return null;var d=tb(a,a===A?J:0);if(0===d)return null;if(0!==(d&30)||0!==(d&a.expiredLanes)||b)b=Zb(a,d);else{b=d;var e=m;m|=2;var f=Ve();if(A!==a||J!==b)oa=null,kb=z()+500,La(a,b);do try{Zf();
break}catch(h){We(a,h)}while(1);xc();$b.current=f;m=e;null!==D?b=0:(A=null,J=0,b=G)}if(0!==b){2===b&&(e=mc(a),0!==e&&(d=e,b=md(a,e)));if(1===b)throw c=mb,La(a,0),xa(a,d),W(a,z()),c;if(6===b)xa(a,d);else{e=a.current.alternate;if(0===(d&30)&&!$f(e)&&(b=Zb(a,d),2===b&&(f=mc(a),0!==f&&(d=f,b=md(a,f))),1===b))throw c=mb,La(a,0),xa(a,d),W(a,z()),c;a.finishedWork=e;a.finishedLanes=d;switch(b){case 0:case 1:throw Error("Root did not complete. This is a bug in React.");case 2:Ma(a,R,oa);break;case 3:xa(a,
d);if((d&130023424)===d&&(b=jd+500-z(),10<b)){if(0!==tb(a,0))break;e=a.suspendedLanes;if((e&d)!==d){ba();a.pingedLanes|=a.suspendedLanes&e;break}a.timeoutHandle=Xe(Ma.bind(null,a,R,oa),b);break}Ma(a,R,oa);break;case 4:xa(a,d);if((d&4194240)===d)break;b=a.eventTimes;for(e=-1;0<d;){var g=31-Ba(d);f=1<<g;g=b[g];g>e&&(e=g);d&=~f}d=e;d=z()-d;d=(120>d?120:480>d?480:1080>d?1080:1920>d?1920:3E3>d?3E3:4320>d?4320:1960*ag(d/1960))-d;if(10<d){a.timeoutHandle=Xe(Ma.bind(null,a,R,oa),d);break}Ma(a,R,oa);break;
case 5:Ma(a,R,oa);break;default:throw Error("Unknown root exit status.");}}}W(a,z());return a.callbackNode===c?Ue.bind(null,a):null}function md(a,b){var c=nb;a.current.memoizedState.isDehydrated&&(La(a,b).flags|=256);a=Zb(a,b);2!==a&&(b=R,R=c,null!==b&&ed(b));return a}function ed(a){null===R?R=a:R.push.apply(R,a)}function $f(a){for(var b=a;;){if(b.flags&16384){var c=b.updateQueue;if(null!==c&&(c=c.stores,null!==c))for(var d=0;d<c.length;d++){var e=c[d],f=e.getSnapshot;e=e.value;try{if(!Z(f(),e))return!1}catch(g){return!1}}}c=
b.child;if(b.subtreeFlags&16384&&null!==c)c.return=b,b=c;else{if(b===a)break;for(;null===b.sibling;){if(null===b.return||b.return===a)return!0;b=b.return}b.sibling.return=b.return;b=b.sibling}}return!0}function xa(a,b){b&=~nd;b&=~Yb;a.suspendedLanes|=b;a.pingedLanes&=~b;for(a=a.expirationTimes;0<b;){var c=31-Ba(b),d=1<<c;a[c]=-1;b&=~d}}function Se(a){if(0!==(m&6))throw Error("Should not already be working.");Ya();var b=tb(a,0);if(0===(b&1))return W(a,z()),null;var c=Zb(a,b);if(0!==a.tag&&2===c){var d=
mc(a);0!==d&&(b=d,c=md(a,d))}if(1===c)throw c=mb,La(a,0),xa(a,b),W(a,z()),c;if(6===c)throw Error("Root did not complete. This is a bug in React.");a.finishedWork=a.current.alternate;a.finishedLanes=b;Ma(a,R,oa);W(a,z());return null}function bg(a){null!==ya&&0===ya.tag&&0===(m&6)&&Ya();var b=m;m|=1;var c=X.transition,d=B;try{if(X.transition=null,B=1,a)return a()}finally{B=d,X.transition=c,m=b,0===(m&6)&&Ea()}}function La(a,b){a.finishedWork=null;a.finishedLanes=0;var c=a.timeoutHandle;-1!==c&&(a.timeoutHandle=
-1,cg(c));if(null!==D)for(c=D.return;null!==c;){var d=c;vc(d);switch(d.tag){case 1:d=d.type.childContextTypes;null!==d&&void 0!==d&&(q(E),q(x));break;case 3:Va();q(E);q(x);Kc();break;case 5:Ic(d);break;case 4:Va();break;case 13:q(w);break;case 19:q(w);break;case 10:zc(d.type._context);break;case 22:case 23:M=ha.current,q(ha)}c=c.return}A=a;D=a=ua(a.current,null);J=M=b;G=0;mb=null;nd=Yb=U=0;R=nb=null;if(null!==Fa){for(b=0;b<Fa.length;b++)if(c=Fa[b],d=c.interleaved,null!==d){c.interleaved=null;var e=
d.next,f=c.pending;if(null!==f){var g=f.next;f.next=e;d.next=g}c.pending=d}Fa=null}return a}function We(a,b){do{var c=D;try{xc();Lb.current=Mb;if(Nb){for(var d=r.memoizedState;null!==d;){var e=d.queue;null!==e&&(e.pending=null);d=d.next}Nb=!1}Ja=0;H=F=r=null;gb=!1;od.current=null;if(null===c||null===c.return){G=1;mb=b;D=null;break}a:{var f=a,g=c.return,h=c,k=b;b=J;h.flags|=32768;if(null!==k&&"object"===typeof k&&"function"===typeof k.then){var l=k,m=h,n=m.tag;if(0===(m.mode&1)&&(0===n||11===n||15===
n)){var q=m.alternate;q?(m.updateQueue=q.updateQueue,m.memoizedState=q.memoizedState,m.lanes=q.lanes):(m.updateQueue=null,m.memoizedState=null)}b:{m=g;do{var u;if(u=13===m.tag){var w=m.memoizedState;u=null!==w?null!==w.dehydrated?!0:!1:!0}if(u){var x=m;break b}m=m.return}while(null!==m);x=null}if(null!==x){x.flags&=-257;k=x;m=b;if(0===(k.mode&1))if(k===g)k.flags|=65536;else{k.flags|=128;h.flags|=131072;h.flags&=-52805;if(1===h.tag)if(null===h.alternate)h.tag=17;else{var B=sa(-1,1);B.tag=2;Ga(h,B,
1)}h.lanes|=1}else k.flags|=65536,k.lanes=m;x.mode&1&&ye(f,l,b);b=x;f=l;var y=b.updateQueue;if(null===y){var t=new Set;t.add(f);b.updateQueue=t}else y.add(f);break a}else{if(0===(b&1)){ye(f,l,b);cd();break a}k=Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.")}}f=k=Tc(k,h);4!==G&&(G=2);null===nb?nb=[f]:nb.push(f);f=g;do{switch(f.tag){case 3:l=k;f.flags|=
65536;b&=-b;f.lanes|=b;var v=we(f,l,b);Qd(f,v);break a;case 1:l=k;var z=f.type,A=f.stateNode;if(0===(f.flags&128)&&("function"===typeof z.getDerivedStateFromError||null!==A&&"function"===typeof A.componentDidCatch&&(null===wa||!wa.has(A)))){f.flags|=65536;b&=-b;f.lanes|=b;var E=xe(f,l,b);Qd(f,E);break a}}f=f.return}while(null!==f)}Ye(c)}catch(sf){b=sf;D===c&&null!==c&&(D=c=c.return);continue}break}while(1)}function Ve(){var a=$b.current;$b.current=Mb;return null===a?Mb:a}function cd(){if(0===G||3===
G||2===G)G=4;null===A||0===(U&268435455)&&0===(Yb&268435455)||xa(A,J)}function Zb(a,b){var c=m;m|=2;var d=Ve();if(A!==a||J!==b)oa=null,La(a,b);do try{dg();break}catch(e){We(a,e)}while(1);xc();m=c;$b.current=d;if(null!==D)throw Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");A=null;J=0;return G}function dg(){for(;null!==D;)Ze(D)}function Zf(){for(;null!==D&&!eg();)Ze(D)}function Ze(a){var b=fg(a.alternate,a,M);a.memoizedProps=a.pendingProps;
null===b?Ye(a):D=b;od.current=null}function Ye(a){var b=a;do{var c=b.alternate;a=b.return;if(0===(b.flags&32768)){if(c=If(c,b,M),null!==c){D=c;return}}else{c=Mf(c,b);if(null!==c){c.flags&=32767;D=c;return}if(null!==a)a.flags|=32768,a.subtreeFlags=0,a.deletions=null;else{G=6;D=null;return}}b=b.sibling;if(null!==b){D=b;return}D=b=a}while(null!==b);0===G&&(G=5)}function Ma(a,b,c){var d=B,e=X.transition;try{X.transition=null,B=1,gg(a,b,c,d)}finally{X.transition=e,B=d}return null}function gg(a,b,c,d){do Ya();
while(null!==ya);if(0!==(m&6))throw Error("Should not already be working.");c=a.finishedWork;var e=a.finishedLanes;if(null===c)return null;a.finishedWork=null;a.finishedLanes=0;if(c===a.current)throw Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");a.callbackNode=null;a.callbackPriority=0;var f=c.lanes|c.childLanes;mf(a,f);a===A&&(D=A=null,J=0);0===(c.subtreeFlags&2064)&&0===(c.flags&2064)||ac||(ac=!0,Te(ld,function(){Ya();return null}));
f=0!==(c.flags&15990);if(0!==(c.subtreeFlags&15990)||f){f=X.transition;X.transition=null;var g=B;B=1;var h=m;m|=4;od.current=null;Nf(a,c);Qe(c,a);a.current=c;Vf(c,a,e);hg();m=h;B=g;X.transition=f}else a.current=c;ac&&(ac=!1,ya=a,bc=e);f=a.pendingLanes;0===f&&(wa=null);gf(c.stateNode,d);W(a,z());if(null!==b)for(d=a.onRecoverableError,c=0;c<b.length;c++)e=b[c],d(e.value,{componentStack:e.stack,digest:e.digest});if(Qb)throw Qb=!1,a=Wc,Wc=null,a;0!==(bc&1)&&0!==a.tag&&Ya();f=a.pendingLanes;0!==(f&1)?
a===kd?lb++:(lb=0,kd=a):lb=0;Ea();return null}function Ya(){if(null!==ya){var a=Fd(bc),b=X.transition,c=B;try{X.transition=null;B=16>a?16:a;if(null===ya)var d=!1;else{a=ya;ya=null;bc=0;if(0!==(m&6))throw Error("Cannot flush passive effects while already rendering.");var e=m;m|=4;for(n=a.current;null!==n;){var f=n,g=f.child;if(0!==(n.flags&16)){var h=f.deletions;if(null!==h){for(var k=0;k<h.length;k++){var l=h[k];for(n=l;null!==n;){var p=n;switch(p.tag){case 0:case 11:case 15:Vb(8,p,f)}var q=p.child;
if(null!==q)q.return=p,n=q;else for(;null!==n;){p=n;var r=p.sibling,u=p.return;Le(p);if(p===l){n=null;break}if(null!==r){r.return=u;n=r;break}n=u}}}var x=f.alternate;if(null!==x){var w=x.child;if(null!==w){x.child=null;do{var A=w.sibling;w.sibling=null;w=A}while(null!==w)}}n=f}}if(0!==(f.subtreeFlags&2064)&&null!==g)g.return=f,n=g;else b:for(;null!==n;){f=n;if(0!==(f.flags&2048))switch(f.tag){case 0:case 11:case 15:Vb(9,f,f.return)}var y=f.sibling;if(null!==y){y.return=f.return;n=y;break b}n=f.return}}var t=
a.current;for(n=t;null!==n;){g=n;var v=g.child;if(0!==(g.subtreeFlags&2064)&&null!==v)v.return=g,n=v;else b:for(g=t;null!==n;){h=n;if(0!==(h.flags&2048))try{switch(h.tag){case 0:case 11:case 15:gd(9,h)}}catch(Yd){Q(h,h.return,Yd)}if(h===g){n=null;break b}var z=h.sibling;if(null!==z){z.return=h.return;n=z;break b}n=h.return}}m=e;Ea();if(da&&"function"===typeof da.onPostCommitFiberRoot)try{da.onPostCommitFiberRoot(rb,a)}catch(Yd){}d=!0}return d}finally{B=c,X.transition=b}}return!1}function $e(a,b,c){b=
Tc(c,b);b=we(a,b,1);a=Ga(a,b,1);b=ba();null!==a&&(vb(a,1,b),W(a,b))}function Q(a,b,c){if(3===a.tag)$e(a,a,c);else for(b=a.return;null!==b;){if(3===b.tag){$e(b,a,c);break}else if(1===b.tag){var d=b.stateNode;if("function"===typeof b.type.getDerivedStateFromError||"function"===typeof d.componentDidCatch&&(null===wa||!wa.has(d))){a=Tc(c,a);a=xe(b,a,1);b=Ga(b,a,1);a=ba();null!==b&&(vb(b,1,a),W(b,a));break}}b=b.return}}function Af(a,b,c){var d=a.pingCache;null!==d&&d.delete(b);b=ba();a.pingedLanes|=a.suspendedLanes&
c;A===a&&(J&c)===c&&(4===G||3===G&&(J&130023424)===J&&500>z()-jd?La(a,0):nd|=c);W(a,b)}function af(a,b){0===b&&(0===(a.mode&1)?b=1:(b=cc,cc<<=1,0===(cc&130023424)&&(cc=4194304)));var c=ba();a=Ua(a,b);null!==a&&(vb(a,b,c),W(a,c))}function Ff(a){var b=a.memoizedState,c=0;null!==b&&(c=b.retryLane);af(a,c)}function Uf(a,b){var c=0;switch(a.tag){case 13:var d=a.stateNode;var e=a.memoizedState;null!==e&&(c=e.retryLane);break;case 19:d=a.stateNode;break;default:throw Error("Pinged unknown suspense boundary type. This is probably a bug in React.");
}null!==d&&d.delete(b);af(a,c)}function Te(a,b){return tc(a,b)}function ig(a,b,c,d){this.tag=a;this.key=c;this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null;this.index=0;this.ref=null;this.pendingProps=b;this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null;this.mode=d;this.subtreeFlags=this.flags=0;this.deletions=null;this.childLanes=this.lanes=0;this.alternate=null}function Xc(a){a=a.prototype;return!(!a||!a.isReactComponent)}function jg(a){if("function"===
typeof a)return Xc(a)?1:0;if(void 0!==a&&null!==a){a=a.$$typeof;if(a===kc)return 11;if(a===lc)return 14}return 2}function ua(a,b){var c=a.alternate;null===c?(c=ka(a.tag,b,a.key,a.mode),c.elementType=a.elementType,c.type=a.type,c.stateNode=a.stateNode,c.alternate=a,a.alternate=c):(c.pendingProps=b,c.type=a.type,c.flags=0,c.subtreeFlags=0,c.deletions=null);c.flags=a.flags&14680064;c.childLanes=a.childLanes;c.lanes=a.lanes;c.child=a.child;c.memoizedProps=a.memoizedProps;c.memoizedState=a.memoizedState;
c.updateQueue=a.updateQueue;b=a.dependencies;c.dependencies=null===b?null:{lanes:b.lanes,firstContext:b.firstContext};c.sibling=a.sibling;c.index=a.index;c.ref=a.ref;return c}function Ib(a,b,c,d,e,f){var g=2;d=a;if("function"===typeof a)Xc(a)&&(g=1);else if("string"===typeof a)g=5;else a:switch(a){case Na:return Ha(c.children,e,f,b);case hc:g=8;e|=8;break;case gc:return a=ka(12,c,b,e|2),a.elementType=gc,a.lanes=f,a;case ic:return a=ka(13,c,b,e),a.elementType=ic,a.lanes=f,a;case jc:return a=ka(19,
c,b,e),a.elementType=jc,a.lanes=f,a;case bf:return Sb(c,e,f,b);default:if("object"===typeof a&&null!==a)switch(a.$$typeof){case zd:g=10;break a;case yd:g=9;break a;case kc:g=11;break a;case lc:g=14;break a;case pa:g=16;d=null;break a}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==a?a:typeof a)+"."));}b=ka(g,c,b,e);b.elementType=a;b.type=d;b.lanes=f;return b}function Ha(a,b,c,d){a=ka(7,a,d,b);a.lanes=
c;return a}function Sb(a,b,c,d){a=ka(22,a,d,b);a.elementType=bf;a.lanes=c;a.stateNode={isHidden:!1};return a}function Fc(a,b,c){a=ka(6,a,null,b);a.lanes=c;return a}function Gc(a,b,c){b=ka(4,null!==a.children?a.children:[],a.key,b);b.lanes=c;b.stateNode={containerInfo:a.containerInfo,pendingChildren:null,implementation:a.implementation};return b}function kg(a,b,c,d,e){this.tag=b;this.containerInfo=a;this.finishedWork=this.pingCache=this.current=this.pendingChildren=null;this.timeoutHandle=-1;this.callbackNode=
this.pendingContext=this.context=null;this.callbackPriority=0;this.eventTimes=nc(0);this.expirationTimes=nc(-1);this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0;this.entanglements=nc(0);this.identifierPrefix=d;this.onRecoverableError=e}function lg(a,b,c,d,e,f,g,h){a=new kg(a,b,!1,f,g);1===b?(b=1,!0===d&&(b|=8)):b=0;d=ka(3,null,null,b);a.current=d;d.stateNode=a;d.memoizedState={element:null,isDehydrated:!1,cache:null,
transitions:null,pendingSuspenseBoundaries:null};Cc(d);return a}function pd(a,b,c,d){var e=b.current,f=ba(),g=Wa(e);a:if(c){c=c._reactInternals;b:{if(qb(c)!==c||1!==c.tag)throw Error("Expected subtree parent to be a mounted class component. This error is likely caused by a bug in React. Please file an issue.");var h=c;do{switch(h.tag){case 3:h=h.stateNode.context;break b;case 1:if(N(h.type)){h=h.stateNode.__reactInternalMemoizedMergedChildContext;break b}}h=h.return}while(null!==h);throw Error("Found unexpected detached subtree parent. This error is likely caused by a bug in React. Please file an issue.");
}if(1===c.tag){var k=c.type;if(N(k)){c=Kd(c,k,h);break a}}c=h}else c=ra;null===b.context?b.context=c:b.pendingContext=c;b=sa(f,g);b.payload={element:a};d=void 0===d?null:d;null!==d&&(b.callback=d);a=Ga(e,b,g);null!==a&&(va(a,e,g,f),Eb(a,e,g));return g}function mg(a){a=Bd(a);a=null!==a?Cd(a):null;return null===a?null:a.stateNode}function ng(a){return null}function dc(a){if(a.isHidden)return null;switch(a.tag){case "TEXT":return a.text;case "INSTANCE":var b=a.props;var c=["children"];if(null==b)b={};
else{var d={},e=Object.keys(b),f;for(f=0;f<e.length;f++){var g=e[f];0<=c.indexOf(g)||(d[g]=b[g])}b=d}c=null;if(a.children&&a.children.length)for(d=0;d<a.children.length;d++)e=dc(a.children[d]),null!==e&&(null===c?c=[e]:c.push(e));a={type:a.type,props:b,children:c};Object.defineProperty(a,"$$typeof",{value:Symbol.for("react.test.json")});return a;default:throw Error("Unexpected node type in toJSON: "+a.tag);}}function ob(a){if(!a)return null;a=cf(a);return 0===a.length?null:1===a.length?ec(a[0]):df(a.map(ec))}
function cf(a){for(var b=[];null!=a;)b.push(a),a=a.sibling;return b}function df(a){var b=[];for(a=[{i:0,array:a}];a.length;)for(var c=a.pop();c.i<c.array.length;){var d=c.array[c.i];c.i+=1;if(cb(d)){a.push(c);a.push({i:0,array:d});break}b.push(d)}return b}function ec(a){if(null==a)return null;switch(a.tag){case 3:return ob(a.child);case 4:return ob(a.child);case 1:return{nodeType:"component",type:a.type,props:Ca({},a.memoizedProps),instance:a.stateNode,rendered:ob(a.child)};case 0:case 15:return{nodeType:"component",
type:a.type,props:Ca({},a.memoizedProps),instance:null,rendered:ob(a.child)};case 5:return{nodeType:"host",type:a.type,props:Ca({},a.memoizedProps),instance:null,rendered:df(cf(a.child).map(ec))};case 6:return a.stateNode.text;case 7:case 10:case 9:case 8:case 12:case 11:case 14:case 17:case 21:return ob(a.child);default:throw Error("toTree() does not yet know how to handle nodes with tag="+a.tag);}}function qd(a){var b=[],c=a;if(null===c.child)return b;c.child.return=c;c=c.child;a:for(;;){var d=
!1;rd.has(c.tag)?b.push(sd(c)):6===c.tag?b.push(""+c.memoizedProps):d=!0;if(d&&null!==c.child)c.child.return=c,c=c.child;else{for(;null===c.sibling;){if(c.return===a)break a;c=c.return}c.sibling.return=c.return;c=c.sibling}}return b}function fc(a,b,c){var d=c?c.deep:!0,e=[];if(b(a)&&(e.push(a),!d))return e;a.children.forEach(function(a){"string"!==typeof a&&e.push.apply(e,fc(a,b,c))});return e}function td(a,b){if(1===a.length)return a[0];throw Error((0===a.length?"No instances found ":"Expected 1 but found "+
a.length+" instances ")+b);}function og(a){console.error(a)}function sd(a){var b=ud.get(a);void 0===b&&null!==a.alternate&&(b=ud.get(a.alternate));void 0===b&&(b=new pg(a),ud.set(a,b));return b}var Ca=Object.assign,za=Aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Jb=Symbol.for("react.element"),$a=Symbol.for("react.portal"),Na=Symbol.for("react.fragment"),hc=Symbol.for("react.strict_mode"),gc=Symbol.for("react.profiler"),zd=Symbol.for("react.provider"),yd=Symbol.for("react.context"),kc=Symbol.for("react.forward_ref"),
ic=Symbol.for("react.suspense"),jc=Symbol.for("react.suspense_list"),lc=Symbol.for("react.memo"),pa=Symbol.for("react.lazy");Symbol.for("react.scope");Symbol.for("react.debug_trace_mode");var bf=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden");Symbol.for("react.cache");Symbol.for("react.tracing_marker");var xd=Symbol.iterator,cb=Array.isArray,tc=la.unstable_scheduleCallback,Re=la.unstable_cancelCallback,eg=la.unstable_shouldYield,hg=la.unstable_requestPaint,z=la.unstable_now,uc=la.unstable_ImmediatePriority,
Xf=la.unstable_UserBlockingPriority,ld=la.unstable_NormalPriority,Yf=la.unstable_IdlePriority,rb=null,da=null,Ba=Math.clz32?Math.clz32:hf,jf=Math.log,kf=Math.LN2,ub=64,cc=4194304,B=0,ae=Oa,Jc=Oa,Ef=Oa,Gf=Oa,Sf=Oa,Rf=Oa,Zd={},qg={},nf=new WeakMap,Xe=setTimeout,cg=clearTimeout,Pf=Gd,Of=Hd,Qf=Id,oc,qc=!1,pf=Object.prototype.hasOwnProperty,rc=[],Pa=-1,ra={},x=qa(ra),E=qa(!1),Da=ra,Z="function"===typeof Object.is?Object.is:of,S=null,yb=!1,sc=!1,wc=[],zb=0,Md=null,Ab=[],Ra=0,Nd=null,na=null,Wf=za.ReactCurrentBatchConfig,
Db=qa(null),Cb=null,Sa=null,yc=null,Fa=null,rf=Ua,ta=!1,Vd=(new Aa.Component).refs,Gb={isMounted:function(a){return(a=a._reactInternals)?qb(a)===a:!1},enqueueSetState:function(a,b,c){a=a._reactInternals;var d=ba(),e=Wa(a),f=sa(d,e);f.payload=b;void 0!==c&&null!==c&&(f.callback=c);b=Ga(a,f,e);null!==b&&(va(b,a,e,d),Eb(b,a,e))},enqueueReplaceState:function(a,b,c){a=a._reactInternals;var d=ba(),e=Wa(a),f=sa(d,e);f.tag=1;f.payload=b;void 0!==c&&null!==c&&(f.callback=c);b=Ga(a,f,e);null!==b&&(va(b,a,e,
d),Eb(b,a,e))},enqueueForceUpdate:function(a,b){a=a._reactInternals;var c=ba(),d=Wa(a),e=sa(c,d);e.tag=2;void 0!==b&&null!==b&&(e.callback=b);b=Ga(a,e,d);null!==b&&(va(b,a,d,c),Eb(b,a,d))}},Xa=Xd(!0),Bf=Xd(!1),db={},ea=qa(db),fb=qa(db),eb=qa(db),w=qa(0),Lc=[],Lb=za.ReactCurrentDispatcher,Sc=za.ReactCurrentBatchConfig,Ja=0,r=null,F=null,H=null,Nb=!1,gb=!1,rg=0,Mb={readContext:T,useCallback:K,useContext:K,useEffect:K,useImperativeHandle:K,useInsertionEffect:K,useLayoutEffect:K,useMemo:K,useReducer:K,
useRef:K,useState:K,useDebugValue:K,useDeferredValue:K,useTransition:K,useMutableSource:K,useSyncExternalStore:K,useId:K,unstable_isNewReconciler:!1},tf={readContext:T,useCallback:function(a,b){fa().memoizedState=[a,void 0===b?null:b];return a},useContext:T,useEffect:ke,useImperativeHandle:function(a,b,c){c=null!==c&&void 0!==c?c.concat([a]):null;return Ob(4,4,ne.bind(null,b,a),c)},useLayoutEffect:function(a,b){return Ob(4,4,a,b)},useInsertionEffect:function(a,b){return Ob(4,2,a,b)},useMemo:function(a,
b){var c=fa();b=void 0===b?null:b;a=a();c.memoizedState=[a,b];return a},useReducer:function(a,b,c){var d=fa();b=void 0!==c?c(b):b;d.memoizedState=d.baseState=b;a={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:a,lastRenderedState:b};d.queue=a;a=a.dispatch=yf.bind(null,r,a);return[d.memoizedState,a]},useRef:function(a){var b=fa();a={current:a};return b.memoizedState=a},useState:ie,useDebugValue:Rc,useDeferredValue:function(a){return fa().memoizedState=a},useTransition:function(){var a=
ie(!1),b=a[0];a=xf.bind(null,a[1]);fa().memoizedState=a;return[b,a]},useMutableSource:function(a,b,c){},useSyncExternalStore:function(a,b,c){c=r;var d=fa();var e=b();if(null===A)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");0!==(Ja&30)||fe(c,b,e);d.memoizedState=e;var f={value:e,getSnapshot:b};d.queue=f;ke(de.bind(null,c,f,a),[a]);c.flags|=2048;ib(9,ee.bind(null,c,f,e,b),void 0,null);return e},useId:function(){var a=fa(),b=A.identifierPrefix,c=rg++;
b=":"+b+"r"+c.toString(32)+":";return a.memoizedState=b},unstable_isNewReconciler:!1},uf={readContext:T,useCallback:pe,useContext:T,useEffect:Qc,useImperativeHandle:oe,useInsertionEffect:le,useLayoutEffect:me,useMemo:qe,useReducer:Oc,useRef:je,useState:function(a){return Oc(hb)},useDebugValue:Rc,useDeferredValue:function(a){var b=V();return re(b,F.memoizedState,a)},useTransition:function(){var a=Oc(hb)[0],b=V().memoizedState;return[a,b]},useMutableSource:be,useSyncExternalStore:ce,useId:se,unstable_isNewReconciler:!1},
vf={readContext:T,useCallback:pe,useContext:T,useEffect:Qc,useImperativeHandle:oe,useInsertionEffect:le,useLayoutEffect:me,useMemo:qe,useReducer:Pc,useRef:je,useState:function(a){return Pc(hb)},useDebugValue:Rc,useDeferredValue:function(a){var b=V();return null===F?b.memoizedState=a:re(b,F.memoizedState,a)},useTransition:function(){var a=Pc(hb)[0],b=V().memoizedState;return[a,b]},useMutableSource:be,useSyncExternalStore:ce,useId:se,unstable_isNewReconciler:!1},zf="function"===typeof WeakMap?WeakMap:
Map,Cf=za.ReactCurrentOwner,O=!1,ad={dehydrated:null,treeContext:null,retryLane:0};var Kf=function(a,b,c,d){for(c=b.child;null!==c;){if(5===c.tag||6===c.tag){d=a;var e=c.stateNode,f=d.children.indexOf(e);-1!==f&&d.children.splice(f,1);d.children.push(e)}else if(4!==c.tag&&null!==c.child){c.child.return=c;c=c.child;continue}if(c===b)break;for(;null===c.sibling;){if(null===c.return||c.return===b)return;c=c.return}c.sibling.return=c.return;c=c.sibling}};var Je=function(a,b){};var Jf=function(a,b,c,d,
e){a.memoizedProps!==d&&(Ia(ea.current),b.updateQueue=qg)&&(b.flags|=4)};var Lf=function(a,b,c,d){c!==d&&(b.flags|=4)};var Tf="function"===typeof WeakSet?WeakSet:Set,n=null,Ke=!1,I=null,ca=!1,ag=Math.ceil,$b=za.ReactCurrentDispatcher,od=za.ReactCurrentOwner,X=za.ReactCurrentBatchConfig,m=0,A=null,D=null,J=0,M=0,ha=qa(0),G=0,mb=null,U=0,Yb=0,nd=0,nb=null,R=null,jd=0,kb=Infinity,oa=null,Qb=!1,Wc=null,wa=null,ac=!1,ya=null,bc=0,lb=0,kd=null,Wb=-1,Xb=0;var fg=function(a,b,c){if(null!==a)if(a.memoizedProps!==
b.pendingProps||E.current)O=!0;else{if(0===(a.lanes&c)&&0===(b.flags&128))return O=!1,Hf(a,b,c);O=0!==(a.flags&131072)?!0:!1}else O=!1;b.lanes=0;switch(b.tag){case 2:var d=b.type;Rb(a,b);a=b.pendingProps;var e=Qa(b,x.current);Ta(b,c);e=Nc(null,b,d,a,e,c);b.flags|=1;if("object"===typeof e&&null!==e&&"function"===typeof e.render&&void 0===e.$$typeof){b.tag=1;b.memoizedState=null;b.updateQueue=null;if(N(d)){var f=!0;xb(b)}else f=!1;b.memoizedState=null!==e.state&&void 0!==e.state?e.state:null;Cc(b);
e.updater=Gb;b.stateNode=e;e._reactInternals=b;Ec(b,d,a,c);b=Zc(null,b,d,!0,f,c)}else b.tag=0,P(null,b,e,c),b=b.child;return b;case 16:d=b.elementType;a:{Rb(a,b);a=b.pendingProps;e=d._init;d=e(d._payload);b.type=d;e=b.tag=jg(d);a=aa(d,a);switch(e){case 0:b=Yc(null,b,d,a,c);break a;case 1:b=Ee(null,b,d,a,c);break a;case 11:b=ze(null,b,d,a,c);break a;case 14:b=Ae(null,b,d,aa(d.type,a),c);break a}throw Error("Element type is invalid. Received a promise that resolves to: "+d+". Lazy element type must resolve to a class or function.");
}return b;case 0:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:aa(d,e),Yc(a,b,d,e,c);case 1:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:aa(d,e),Ee(a,b,d,e,c);case 3:Fe(b);if(null===a)throw Error("Should have a current fiber. This is a bug in React.");e=b.pendingProps;d=b.memoizedState.element;Pd(a,b);Fb(b,e,null,c);e=b.memoizedState.element;e===d?b=ma(a,b,c):(P(a,b,e,c),b=b.child);return b;case 5:return $d(b),d=b.pendingProps.children,De(a,b),P(a,b,d,c),b.child;case 6:return null;
case 13:return Ge(a,b,c);case 4:return Hc(b,b.stateNode.containerInfo),d=b.pendingProps,null===a?b.child=Xa(b,null,d,c):P(a,b,d,c),b.child;case 11:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:aa(d,e),ze(a,b,d,e,c);case 7:return P(a,b,b.pendingProps,c),b.child;case 8:return P(a,b,b.pendingProps.children,c),b.child;case 12:return P(a,b,b.pendingProps.children,c),b.child;case 10:a:{d=b.type._context;e=b.pendingProps;f=b.memoizedProps;var g=e.value;u(Db,d._currentValue2);d._currentValue2=g;
if(null!==f)if(Z(f.value,g)){if(f.children===e.children&&!E.current){b=ma(a,b,c);break a}}else for(f=b.child,null!==f&&(f.return=b);null!==f;){var h=f.dependencies;if(null!==h){g=f.child;for(var k=h.firstContext;null!==k;){if(k.context===d){if(1===f.tag){k=sa(-1,c&-c);k.tag=2;var l=f.updateQueue;if(null!==l){l=l.shared;var m=l.pending;null===m?k.next=k:(k.next=m.next,m.next=k);l.pending=k}}f.lanes|=c;k=f.alternate;null!==k&&(k.lanes|=c);Ac(f.return,c,b);h.lanes|=c;break}k=k.next}}else if(10===f.tag)g=
f.type===b.type?null:f.child;else if(18===f.tag){g=f.return;if(null===g)throw Error("We just came from a parent so we must have had a parent. This is a bug in React.");g.lanes|=c;h=g.alternate;null!==h&&(h.lanes|=c);Ac(g,c,b);g=f.sibling}else g=f.child;if(null!==g)g.return=f;else for(g=f;null!==g;){if(g===b){g=null;break}f=g.sibling;if(null!==f){f.return=g.return;g=f;break}g=g.return}f=g}P(a,b,e.children,c);b=b.child}return b;case 9:return e=b.type,d=b.pendingProps.children,Ta(b,c),e=T(e),d=d(e),
b.flags|=1,P(a,b,d,c),b.child;case 14:return d=b.type,e=aa(d,b.pendingProps),e=aa(d.type,e),Ae(a,b,d,e,c);case 15:return Be(a,b,b.type,b.pendingProps,c);case 17:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:aa(d,e),Rb(a,b),b.tag=1,N(d)?(a=!0,xb(b)):a=!1,Ta(b,c),Td(b,d,e),Ec(b,d,e,c),Zc(null,b,d,!0,a,c);case 19:return Ie(a,b,c);case 22:return Ce(a,b,c)}throw Error("Unknown unit of work tag ("+b.tag+"). This error is likely caused by a bug in React. Please file an issue.");};var ka=function(a,
b,c,d){return new ig(a,b,c,d)},sg=Aa.unstable_act,tg=function(){return null},rd=new Set([0,1,5,11,14,15,3]),pg=function(){function a(a){if(!rd.has(a.tag))throw Error("Unexpected object passed to ReactTestInstance constructor (tag: "+a.tag+"). This is probably a bug in React.");this._fiber=a}var b=a.prototype;b._currentFiber=function(){var a=Bd(this._fiber);if(null===a)throw Error("Can't read from currently-mounting component. This error is likely caused by a bug in React. Please file an issue.");
return a};b.find=function(a){return td(this.findAll(a,{deep:!1}),"matching custom predicate: "+a.toString())};b.findByType=function(a){return td(this.findAllByType(a,{deep:!1}),'with node type: "'+(pb(a)||"Unknown")+'"')};b.findByProps=function(a){return td(this.findAllByProps(a,{deep:!1}),"with props: "+JSON.stringify(a))};b.findAll=function(a){return fc(this,a,1<arguments.length&&void 0!==arguments[1]?arguments[1]:null)};b.findAllByType=function(a){return fc(this,function(b){return b.type===a},
1<arguments.length&&void 0!==arguments[1]?arguments[1]:null)};b.findAllByProps=function(a){return fc(this,function(b){var c;if(c=b.props)a:{for(var d in a)if(b.props[d]!==a[d]){c=!1;break a}c=!0}return c},1<arguments.length&&void 0!==arguments[1]?arguments[1]:null)};ef(a,[{key:"instance",get:function(){return 5===this._fiber.tag?wb(this._fiber.stateNode):this._fiber.stateNode}},{key:"type",get:function(){return this._fiber.type}},{key:"props",get:function(){return this._currentFiber().memoizedProps}},
{key:"parent",get:function(){for(var a=this._fiber.return;null!==a;){if(rd.has(a.tag)){if(3===a.tag&&2>qd(a).length)break;return sd(a)}a=a.return}return null}},{key:"children",get:function(){return qd(this._currentFiber())}}]);return a}(),ud=new WeakMap;(function(a){a={bundleType:a.bundleType,version:a.version,rendererPackageName:a.rendererPackageName,rendererConfig:a.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,
overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:za.ReactCurrentDispatcher,findHostInstanceByFiber:mg,findFiberByHostInstance:a.findFiberByHostInstance||ng,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0"};if("undefined"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)a=!1;else{var b=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(b.isDisabled||
!b.supportsFiber)a=!0;else{try{rb=b.inject(a),da=b}catch(c){}a=b.checkDCE?!0:!1}}return a})({findFiberByHostInstance:function(){throw Error("TestRenderer does not support findFiberByHostInstance()");},bundleType:0,version:"18.2.0-next-9e3b772b8-20220608",rendererPackageName:"react-test-renderer"});Y._Scheduler=vd;Y.act=sg;Y.create=function(a,b){var c=tg,d=!1,e=!1;"object"===typeof b&&null!==b&&("function"===typeof b.createNodeMock&&(c=b.createNodeMock),!0===b.unstable_isConcurrent&&(d=!0),!0===b.unstable_strictMode&&
(e=!0));var f={children:[],createNodeMock:c,tag:"CONTAINER"},g=lg(f,d?1:0,null,e,null,"",og);if(null==g)throw Error("something went wrong");pd(a,g,null,null);a={_Scheduler:vd,root:void 0,toJSON:function(){if(null==g||null==g.current||null==f||0===f.children.length)return null;if(1===f.children.length)return dc(f.children[0]);if(2===f.children.length&&!0===f.children[0].isHidden&&!1===f.children[1].isHidden)return dc(f.children[1]);var a=null;if(f.children&&f.children.length)for(var b=0;b<f.children.length;b++){var c=
dc(f.children[b]);null!==c&&(null===a?a=[c]:a.push(c))}return a},toTree:function(){return null==g||null==g.current?null:ec(g.current)},update:function(a){null!=g&&null!=g.current&&pd(a,g,null,null)},unmount:function(){null!=g&&null!=g.current&&(pd(null,g,null,null),g=f=null)},getInstance:function(){if(null==g||null==g.current)return null;a:{var a=g.current;if(a.child)switch(a.child.tag){case 5:a=wb(a.child.stateNode);break a;default:a=a.child.stateNode}else a=null}return a},unstable_flushSync:bg};
Object.defineProperty(a,"root",{configurable:!0,enumerable:!0,get:function(){if(null===g)throw Error("Can't access .root on unmounted test renderer");var a=qd(g.current);if(0===a.length)throw Error("Can't access .root on unmounted test renderer");return 1===a.length?a[0]:sd(g.current)}});return a};Y.unstable_batchedUpdates=function(a,b){var c=m;m|=1;try{return a(b)}finally{m=c,0===m&&(kb=z()+500,yb&&Ea())}}});
})();
