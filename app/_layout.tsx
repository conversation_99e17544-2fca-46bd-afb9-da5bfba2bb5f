import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { ThemeProvider } from '../src/components/ThemeProvider';
import { AuthProvider } from '../src/contexts/AuthContext';

const RootLayout = (): JSX.Element => (
  <AuthProvider>
    <ThemeProvider>
      <Stack
        screenOptions={{
          headerStyle: {
            backgroundColor: '#6366f1',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="index"
          options={{
            title: 'Bible Companion',
          }}
        />
        <Stack.Screen
          name="login"
          options={{
            title: 'Login',
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="auth-login"
          options={{
            title: 'Sign In',
            presentation: 'modal',
          }}
        />
      </Stack>
      <StatusBar style="light" />
    </ThemeProvider>
  </AuthProvider>
);

export default RootLayout;
