// Setup global test environment first
global.__DEV__ = true;

// Note: React Testing Library matchers will be imported in individual test files
// to avoid conflicts with React Native mocking

// Mock Expo modules
jest.mock('expo-constants', () => ({
  expoConfig: {
    name: 'Bible Companion',
    slug: 'bible-companion',
  },
}));

jest.mock('expo-linking', () => ({
  createURL: jest.fn(),
  openURL: jest.fn(),
}));

jest.mock('expo-router', () => ({
  Stack: ({ children }) => children,
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
  Link: ({ children }) => children,
  router: {
    back: jest.fn(),
  },
}));

jest.mock('expo-status-bar', () => ({
  StatusBar: () => null,
}));

// Mock React Native modules
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock React Native components that might cause issues in tests
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');

  // Mock components that don't work well in test environment
  RN.UIManager = RN.UIManager || {};
  RN.UIManager.measure = jest.fn();
  RN.UIManager.measureInWindow = jest.fn();
  RN.UIManager.measureLayout = jest.fn();
  RN.UIManager.setLayoutAnimationEnabledExperimental = jest.fn();

  // Mock Alert
  RN.Alert = {
    alert: jest.fn(),
  };

  return RN;
});

// Mock Firebase
jest.mock('firebase/app', () => ({
  initializeApp: jest.fn(),
  getApps: jest.fn(() => []),
}));

jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(),
  initializeAuth: jest.fn(),
  getReactNativePersistence: jest.fn(),
  signInWithEmailAndPassword: jest.fn(),
  createUserWithEmailAndPassword: jest.fn(),
  signOut: jest.fn(),
  onAuthStateChanged: jest.fn(),
  PhoneAuthProvider: {
    credential: jest.fn(),
  },
  signInWithCredential: jest.fn(),
  signInWithPhoneNumber: jest.fn(),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

// Mock react-firebase-hooks
jest.mock('react-firebase-hooks/auth', () => ({
  useAuthState: jest.fn(() => [null, false, null]),
}));

// Mock MongoDB
jest.mock('mongodb', () => ({
  MongoClient: jest.fn().mockImplementation(() => ({
    connect: jest.fn(),
    close: jest.fn(),
    db: jest.fn(() => ({
      collection: jest.fn(() => ({
        findOne: jest.fn(),
        find: jest.fn(() => ({
          toArray: jest.fn(() => []),
          limit: jest.fn(() => ({
            toArray: jest.fn(() => []),
          })),
        })),
        insertOne: jest.fn(),
        updateOne: jest.fn(),
        deleteOne: jest.fn(),
        createIndex: jest.fn(),
      })),
      admin: jest.fn(() => ({
        ping: jest.fn(),
      })),
    })),
  })),
  ObjectId: jest.fn().mockImplementation(id => ({ _id: id || 'mock-object-id' })),
}));

// Additional global setup can go here if needed
